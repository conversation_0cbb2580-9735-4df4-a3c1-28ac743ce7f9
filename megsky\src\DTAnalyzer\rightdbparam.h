#ifndef RIGHTDBPARAM_H
#define RIGHTDBPARAM_H

#include "pub_std.h"

//=====================================================================================
//???????????
//=====================================================================================
struct tagRightParam {
	CString<64>		m_Name;          // ????
    Jint32			m_id;
};

//=====================================================================================
//???????
//=====================================================================================
struct tagModuleParam {
    CString<64>		modelname;   // ????
	CString<64>		m_Name;      // ????
    Jint32			m_id;
    CString<64>		type;   
    CString<64>		unit;
    CString<64>		deadzone;
    CString<64>		ratio;
    CString<64>		isReport;
    CString<64>		userdefine;

};

//=====================================================================================
//DEV_NAME?????
//=====================================================================================
struct tagDevNameParam {
    CString<64>		model;          // ????
    CString<64>		port;           // ????
    CString<64>		addr;           // ????
    CString<64>		desc;           // ????
    Jint32			m_id;
    CString<64>		manuID;
    CString<64>		manuName;
    CString<64>		ProType;
    CString<64>		deviceType;
    CString<64>		isReport;
    CString<64>		nodeID;
    CString<64>		productID;
    CString<64>		guid;
    CString<64>		dev;
};

//=====================================================================================
//????????
//=====================================================================================
struct tagUserParam {
	CString<64>		dev;   //????
    CString<64>		name;   //????
    Jint32			m_id;
    CString<64>		val;
    CString<64>		unit;
    CString<64>		datatype;
};

struct tagRightMapParam {
    CString<64>			model;		//
    CString<64>			dev;		        //      ????
    CString<64>			event;		        //      ????
    CString<64>			timestamp;		    //      ????  ????????? %.4d-%.2d-%.2d %.2d:%.2d:%.2d.%.3d
    Jint32              m_id;           //????ID
    CString<64>			timestartgather;		//
    CString<64>			timeendgather;		//
    CString<64>			starttimestamp;		//
    CString<64>			endtimestamp;		//
    CString<64>			HappenSrc;		//
    CString<64>			IsNeedRpt;		//
    CString<64>			occurnum;		//
    CString<64>			EventLevel;     //
    CString<64>			Net_1;		//
};

struct SoeContentParam {
    CString<64>			dev;               //      ????
    CString<64>			event;               //      ????
    CString<64>			etimestamp;               //      ????
    CString<64>			name;               //      ????
    Jint32              m_id;               //????ID
    CString<64>			val;               
    CString<64>			desc;
    CString<64>			timestamp;
};


//=====================================================================================
//DEV_DATA
//=====================================================================================
struct tagRealParam {
	CString<64>		dev;   //????
    CString<64>		name;   //????
    CString<64>		model;   //????
    Jint32			m_id;
    Jint32			secret; //????
    CString<64>		val;
    CString<64>		quality;
    CString<64>		num;
    CString<64>		timestamp;
};

//=====================================================================================
//ASN_DATA_0015  ?????????
//=====================================================================================
struct tagAsnData0015Param      //????
{
    Jint32			id;
    Jint32			devNo;      //????υτ??
    Jint32			starttime;  //?????????
    Jint32			endtime;    //??????????
    Jint32			timer;      //????›¥???
    Jint32			infoID;     //??????
    Jint32			classID;    //????????
    Jint32			mainD_ID;   //??????ID
    CString<64>		checkcode;  //????§µ??
};

struct tagAsnData0015DataParam  //???????m_id ??????id??? 
{
    Jint32			id;         //????-
    Jint32			m_id;       //m_id ??????id???     
    Jint32          addr;       //????????
    Jint32          property;   //??????
    CString<256>	str_value;  //???????
};

//=====================================================================================
//ASN_DATA_0011     //?????????
//=====================================================================================
struct tagAsnData0011Param      //????
{
    Jint32			id;         //????
    Jint32			devNo;      //????-????υτ??
    Jint32			infoID;     //????-??????
    Jint32          addr;       //????-????????
    Jint32          property;   //??????
    Jint32			classID;    //????????
    Jint32		    value;      //???????
};


#endif //RIGHTDBPARAM_H
