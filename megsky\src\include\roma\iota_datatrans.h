/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012.
 */

#ifndef INC_AGENT_IOTA_DATATRANS_H
#define INC_AGENT_IOTA_DATATRANS_H

#include "hw_type.h"


#ifdef __cplusplus
extern "C" {
#endif
typedef struct BatchReportProperties_S {
	HW_CHAR *p_deviceid; 
	HW_CHAR *p_serviceid;
	HW_CHAR *p_payload;
} ST_IOTA_BatchReportProperties;

/**
* @brief	Gateway sub-device reports data.
*
* @param [in] pcDeviceId          : Device Id
* @param [in] pcServiceId         : Service Id
* @param [in] pcServiceProperties : Service attributes
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_ServiceDataReport(HW_CHAR *pcDeviceId, HW_CHAR *pcServiceId, HW_CHAR *pcServiceProperties);

/**
* @brief	Gateway sub-device reports data with qos.
*
* @param [in] pcDeviceId          : Device Id
* @param [in] pcServiceId         : Service Id
* @param [in] pcServiceProperties : Service attributes
* @param [in] qos                   : qos
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_ServiceDataReport_Qos(HW_CHAR *pcDeviceId, HW_CHAR *pcServiceId, HW_CHAR *pcServiceProperties, HW_INT qos);


/**
* @brief	Sub-device data batch reporting interface
.
*
* @param [in] batchReportProperties : Multi-device info (see ST_IOTA_BatchReportProperties)
* @param [in] maxnum                : Max device num
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_BatchPropertyReport(ST_IOTA_BatchReportProperties *batchReportProperties, HW_INT maxnum);

/**
* @brief	Sub-device data batch reporting interface with qos
.
*
* @param [in] batchReportProperties : Multi-device info (see ST_IOTA_BatchReportProperties)
* @param [in] maxnum                : Max device num
* @param [in] qos                   : qos
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_BatchPropertyReport_Qos(ST_IOTA_BatchReportProperties *batchReportProperties, HW_INT maxnum, HW_INT qos);

/* This interface function is temporarily unavailable */
HW_API_FUNC HW_INT IOTA_ServiceBatchDataReport(char *pcPayload);

/* This interface function is temporarily unavailable */
HW_API_FUNC HW_INT IOTA_ServiceBatchDataReport_Qos(char *pcPayload , HW_INT qos);


/**
* @brief	Command Respense.
*
* @param [in] uiMid             : Same as mid in the received order
* @param [in] uiResultCode      : process result
* @param [in] pcCommandRespense : Reply Command Properties
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_ServiceCommandRespense(HW_UINT uiMid, HW_UINT uiResultCode, HW_CHAR *pcCommandRespense);

/**
* @brief	Command Respense with qos.
*
* @param [in] uiMid             : Same as mid in the received order
* @param [in] uiResultCode      : process result
* @param [in] pcCommandRespense : Reply Command Properties
* @param [in] qos                   : qos
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_ServiceCommandRespense_Qos(HW_UINT uiMid, HW_UINT uiResultCode, HW_CHAR *pcCommandRespense, HW_INT qos);


/**
* @brief	Gateway sub-device reports data.
*
* @param [in] dev_info		      : Device Info  (see ST_IOTA_BatchReportProperties)
* @param [in] topicVersion        : topic version
* @param [in] customTopic         : Custom Topic
*
* @retval messageId : >= 0 success.
* @retval -1        : Failed.
*/
HW_API_FUNC HW_INT IOTA_ServiceCustomTopicReport(ST_IOTA_BatchReportProperties *dev_info, HW_CHAR *topicVersion, HW_CHAR *customTopic);

/**
* @brief	Gateway sub-device reports data with qos.
*
* @param [in] dev_info		      : Device Info  (see ST_IOTA_BatchReportProperties)
* @param [in] topicVersion		  : topic version
* @param [in] customTopic		  : Custom Topic
* @param [in] qos				  : qos
*
* @retval messageId : >= 0 success.
* @retval -1		: Failed.
*/
HW_API_FUNC HW_INT IOTA_ServiceCustomTopicReport_Qos(ST_IOTA_BatchReportProperties *dev_info, HW_CHAR *topicVersion, HW_CHAR *customTopic, HW_INT qos);

/**
* @brief	Gateway device Subscribe Custom Topic.
*
* @param [in] topicVersion		  : topic version
* @param [in] customTopic		  : Custom Topic
*
* @retval messageId : >= 0 success.
* @retval -1		: Failed.
*/
HW_API_FUNC HW_INT IOTA_SubscribeCustomTopic(HW_CHAR *topicVersion, HW_CHAR *customTopic);

#ifdef __cplusplus
}
#endif
#endif
