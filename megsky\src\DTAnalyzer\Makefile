##############################################################################
# Copyright (c) 2021 山东梅格彤天电气有限公司 http://www.megsky.com
#
# @file    : Makefile
# @brief   : Data board library Makefile
# @note
#

##############################################################################

TZCDIR			 = ../..
APPNAME			 = DTAnalyzer
CROSS_COMPILE	?= aarch64-linux-gnu-
TARGET  		?= $(TZCDIR)/bin/$(APPNAME)



CC 				:= $(CROSS_COMPILE)g++

INCDIRS		:=  $(TZCDIR)/src/include \
				$(TZCDIR)/src/include/tzc_std \
				$(TZCDIR)/src/include/meg_pub \
				$(TZCDIR)/src/third_party/CJsonObject \
				./include\

SRCDIRS		:=  $(TZCDIR)/src/$(APPNAME)  \
				$(TZCDIR)/src/third_party/CJsonObject \


INCLUDE		:= $(patsubst %, -I %, $(INCDIRS))

CPPFILES	:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.cpp))
CPPFILENDIR	:= $(notdir  $(CPPFILES))
CPPOBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CPPFILENDIR:.cpp=.o))

CFILES		:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))
CFILENDIR	:= $(notdir  $(CFILES))
COBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CFILENDIR:.c=.o))

OBJS		:= $(CPPOBJS) $(COBJS)

CFLAGS  	:= -Wall -Wl,-rpath=$(TZCDIR)/lib:$(TZCDIR)/lib/third_lib
LDFLAGS		:=-L$(TZCDIR)/lib -L$(TZCDIR)/lib/third_lib -L/usr/lib -lpthread -lpaho-mqtt3c -ltzcstd -lmegpub -lssl -lcrypto -lhd \


VPATH		:= $(SRCDIRS)

.PHONY : clean packet dtanalyzer


$(TARGET) : $(OBJS)
	$(CC) $(CFLAGS) $(OBJS) $(LDFLAGS) -o $@ 

$(CPPOBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.cpp
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

$(COBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 


pac :
	objdump -d $(APPNAME)  > MM.s
	
clean :
	rm -rf $(OBJS)
	rm -rf $(TARGET)

packet :
	rm -f $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)
	rm -f $(TZCDIR)/ova/$(APPNAME).tar

	chmod 777 $(TZCDIR)/ova/$(APPNAME)/bin/startup_app.sh
	chmod 777 $(TZCDIR)/ova/$(APPNAME)/bin/stop_app.sh

	cp $(TARGET) $(TZCDIR)/ova/$(APPNAME)/bin/$(APPNAME)


# 		./appSignTool_x86 \
# 		-f $(TZCDIR)/ova/$(APPNAME)/bin \
# 		-b $(APPNAME) \
# 		-l $(TZCDIR)/ova/$(APPNAME)/lib\
# 		-e $(TZCDIR)/ova/$(APPNAME)/configFile \
# 		-v 	T1.0.0.1\
# 		-o $(APPNAME)

# 	cp $(TZCDIR)/src/$(APPNAME)/$(APPNAME).tar $(TZCDIR)/ova/bin/
# 	cd $(TZCDIR)/ova/bin
#  	tar -cvf $(APPNAME).tar $(APPNAME)

	./appSignTool_x86 -f $(TZCDIR)/ova/$(APPNAME)/bin -b $(APPNAME) -v T1.0.0.4 -o $(APPNAME) -e $(TZCDIR)/ova/$(APPNAME)/configFile -l $(TZCDIR)/ova/$(APPNAME)/lib 
	rm -rf ./$(APPNAME)/bin/$(APPNAME).tar
	cp ./$(APPNAME).tar ./$(APPNAME)/bin
	tar -cvf $(APPNAME).tar $(APPNAME)





		

