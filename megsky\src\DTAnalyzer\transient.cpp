#include "transient.h"
#include "mqtt_pub_inter.h"
#include "param_json.h"
#include <iostream>
#include <iomanip>
#include <cstring>
#include <chrono>
#include <thread>
#include <cmath>
#include <algorithm>
#include <ctime>

using namespace std;



const char* TransientConfig::CHANNEL_NAMES[CHANNEL_COUNT] = {
    "UA", "UB", "UC",           // 三相电压
    "IA", "IB", "IC"            // 三相电流
};

//=============================================================================
// CircularBuffer 类实现
//=============================================================================

CircularBuffer::CircularBuffer(size_t capacity)
    : m_capacity(capacity)
    , m_head(0)
    , m_tail(0)
    , m_size(0)
{
    if (capacity == 0) {
        throw std::invalid_argument("CircularBuffer capacity cannot be zero");
    }
    m_buffer.resize(capacity);
   // IEC_LOG_RECORD(eRunType, "CircularBuffer created with capacity: %zu", capacity);
}

CircularBuffer::~CircularBuffer()
{
    Clear();
    //IEC_LOG_RECORD(eRunType, "CircularBuffer destroyed");
}

void CircularBuffer::Push(const WaveformSample& sample)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    m_buffer[m_head] = sample;
    m_head = (m_head + 1) % m_capacity;

    if (m_size < m_capacity) {
        ++m_size;
    } else {
        // 缓冲区已满，移动尾指针
        m_tail = (m_tail + 1) % m_capacity;
    }
}

bool CircularBuffer::GetSamples(std::vector<WaveformSample>& samples, size_t count)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (count > m_size) {
        return false;
    }

    samples.clear();
    samples.reserve(count);

    size_t index = (m_tail + m_size - count) % m_capacity;
    for (size_t i = 0; i < count; ++i) {
        samples.push_back(m_buffer[index]);
        index = (index + 1) % m_capacity;
    }

    return true;
}

bool CircularBuffer::GetAllSamples(std::vector<WaveformSample>& samples)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    samples.clear();
    samples.reserve(m_size);

    size_t index = m_tail;
    for (size_t i = 0; i < m_size; ++i) {
        samples.push_back(m_buffer[index]);
        index = (index + 1) % m_capacity;
    }

    return true;
}

void CircularBuffer::Clear()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_head = 0;
    m_tail = 0;
    m_size = 0;
}

size_t CircularBuffer::Size() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size;
}

bool CircularBuffer::IsFull() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_size == m_capacity;
}

void CircularBuffer::SetCapacity(size_t new_capacity)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (new_capacity == m_capacity) {
        return; // 容量没有变化
    }

    // 创建新的缓冲区
    std::vector<WaveformSample> new_buffer(new_capacity);

    // 复制现有数据到新缓冲区
    size_t copy_size = std::min(m_size, new_capacity);
    size_t new_head = 0;

    if (copy_size > 0) {
        // 从尾部开始复制最新的数据
        size_t start_index = (m_tail + m_size - copy_size) % m_capacity;
        for (size_t i = 0; i < copy_size; ++i) {
            new_buffer[new_head] = m_buffer[(start_index + i) % m_capacity];
            new_head = (new_head + 1) % new_capacity;
        }
    }

    // 更新缓冲区
    m_buffer = std::move(new_buffer);
    m_capacity = new_capacity;
    m_head = new_head;
    m_tail = 0;
    m_size = copy_size;

    // IEC_LOG_RECORD(eRunType, "CircularBuffer capacity changed to %zu, current size: %zu",
    //                new_capacity, m_size);
}

//=============================================================================
// CTransientThread 类实现
//=============================================================================

CTransientThread::CTransientThread()
    : m_pMng(nullptr)
{
}

CTransientThread::~CTransientThread()
{
}

void CTransientThread::SetTransientManager(CTransientManager* p)
{
    m_pMng = p;
}

void CTransientThread::run(void)
{
    if (m_pMng) {
        m_pMng->thread_prev();
        while (CSL_thread::IsEnableRun()) {
            m_pMng->thread_func();
        }
        m_pMng->thread_exit();
    }
}

std::string CTransientThread::ThreadName(void) const
{
    return std::string("暂态数据分析线程");
}

void CTransientThread::RecordLog(const std::string& sMsg)
{
   // IEC_LOG_RECORD(eRunType, "%s", sMsg.c_str());
}

//=============================================================================
// CTransientManager 类实现
//=============================================================================

CTransientManager::CTransientManager()
    : m_pThread(nullptr)
    , m_pBuffer(nullptr)
    , m_bRunning(false)
    , m_bTriggerActive(false)
    , m_triggerTime(0)
    , m_postTriggerCount(0)
    , m_lastCycleSeq(0)
{
}

CTransientManager::~CTransientManager()
{
    Exit();
}

CTransientManager& CTransientManager::CreateInstance()
{
    static CTransientManager instance;
    return instance;
}

bool CTransientManager::Init(void)
{
    try {
        cout<<"初始化成功";
        // 初始化环形缓冲区
        m_pBuffer.reset(new CircularBuffer(m_config.buffer_capacity));

        // 初始化线程
        m_pThread.reset(new CTransientThread());
        m_pThread->SetTransientManager(this);

        // // 初始化共享内存
        // if (!InitSharedMemory()) {
        //     IEC_LOG_RECORD(eErrType, "Failed to initialize shared memory");
        //     return false;
        // }

        // 初始化HAL设备
        if (!InitHalDevice()) {
            //IEC_LOG_RECORD(eErrType, "Failed to initialize HAL device");
            return false;
        }

        IEC_LOG_RECORD(eRunType, "Transient manager initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        IEC_LOG_RECORD(eErrType, "Transient manager init failed: %s", e.what());
        return false;
    }
}

void CTransientManager::Exit(void)
{
    Stop();

    if (m_pThread) {
        m_pThread.reset();
    }

    if (m_pBuffer) {
        m_pBuffer.reset();
    }
}

void CTransientManager::Start(void)
{
    if (!m_bRunning && m_pThread) {
        m_bRunning = true;
        m_pThread->start();
        cout<<"启动暂态成功";
    }
}

void CTransientManager::Stop(void)
{
    if (m_bRunning && m_pThread) {
        m_bRunning = false;
        m_pThread->close();
    }
}

void CTransientManager::thread_prev(void)
{
    m_bTriggerActive = false;
    m_triggerTime = 0;
    m_postTriggerCount = 0;
}

void CTransientManager::thread_func(void)
{
    static auto last_read_time = std::chrono::steady_clock::now();
    auto current_time = std::chrono::steady_clock::now();
   // IEC_LOG_RECORD(eRunType, "Transient thread running...");

    try {
        // 检查是否到了读取波形数据的时间（每10毫秒）
        auto time_diff = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_read_time);
        if (time_diff.count() >= 10) {
            // 每10毫秒读取一个波形数据
            if (ReadWaveformFromHal(1)) {
                //IEC_LOG_RECORD(eRunType, "Successfully read waveform data in thread");
            } else {
                //IEC_LOG_RECORD(eErrType, "Failed to read waveform data in thread");
            }
            last_read_time = current_time;
        }

        // // 执行暂态事件判断（仅判断，不存储）
        // JudgeTansientEvent();

        // 短暂休眠，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(1));  // 休眠1毫秒
    }
    catch (const std::exception& e) {
       // IEC_LOG_RECORD(eErrType, "Exception in thread_func: %s", e.what());
        std::this_thread::sleep_for(std::chrono::milliseconds(10));  // 出错时休眠更长时间
    }
}

void CTransientManager::thread_exit(void)
{
    //IEC_LOG_RECORD(eRunType, "Transient thread exiting...");
}

void CTransientManager::OnRun(void)
{
    while (m_bRunning) {
        thread_func();
    }
}

bool CTransientManager::InitHalDevice() {
    // 初始化HAL库
    int ret = hal_init();
    if (ret != 0) {
        //IEC_LOG_RECORD(eErrType, "hal_init failed, ret=%d", ret);
        return false;
    }

    // 获取波形采样设备
    m_pWaveformDevice = (WAVEFORM_SAMPLER_DEVICE_T *)hal_device_get(HW_DEVICE_ID_WAVEFORM_SAMPLER);
    if (!m_pWaveformDevice) {
        //IEC_LOG_RECORD(eErrType, "Cannot find %s", HW_DEVICE_ID_WAVEFORM_SAMPLER);
        hal_exit();
        return false;
    }

    //IEC_LOG_RECORD(eRunType, "HAL waveform device initialized successfully");
    return true;
}

bool CTransientManager::ReadWaveformFromHal(uint8_t num_cycles)
{
    if (!m_pWaveformDevice) {
        //IEC_LOG_RECORD(eErrType, "Waveform device not initialized");
        return false;
    }

    // 定义帧结构尺寸
    const size_t WAVE_DATA_SIZE = 7182; 

    // 计算需要读取的字节数
    size_t read_size = WAVE_HEADER_SIZE + (WAVE_DATA_SIZE * num_cycles) + WAVE_TAIL_SIZE;

    // 分配读取缓冲区
    std::vector<uint8_t> read_buffer(read_size);

    // 从HAL设备读取波形数据
    int ret = m_pWaveformDevice->read_waveform(m_pWaveformDevice,
                                               read_buffer.data(),
                                               read_size,
                                               num_cycles);

    if (ret <= 0) {
        //IEC_LOG_RECORD(eErrType, "Failed to read waveform data, ret=%d", ret);
        return false;
    }

    //IEC_LOG_RECORD(eRunType, "Successfully read %d bytes of waveform data", ret);

    // 处理读取到的数据
    ReceiveWaveformPacket(read_buffer.data(), ret);


    read_buffer.clear();

    return true;
}

bool CTransientManager::ReceiveWaveformPacket(const uint8_t* packet_data, size_t packet_length)
{
    printf("===== 收到数据报文 =====\n");
    printf("读取到 %zu 字节\n", packet_length);

    // 基本长度检查
    if(!packet_data || packet_length < WAVE_HEADER_SIZE + WAVE_TAIL_SIZE) {
        printf("错误: 数据长度不足以包含帧头和帧尾\n");
        return false;
    }

    // 解析帧头
    wave_header_t* header = (wave_header_t*)packet_data;
    if (header->start_marker != 0x68) {
        printf("警告: 无效帧头 (0x%02X != 0x68)\n", header->start_marker);
        return false;
    }

    if (header->command != 0x36) {
        printf("警告: 无效命令符 (0x%02X != 0x36)\n", header->command);
        return false;
    }

    printf("帧头验证通过: 起始符=0x%02X, 命令符=0x%02X\n", header->start_marker, header->command);
    printf("长度域: %u, 帧序号: %u\n", header->data_length, header->frame_seq);

    // 解析帧尾和校验和验证
    wave_tail_t* tail = (wave_tail_t*)(packet_data + packet_length - WAVE_TAIL_SIZE);
    if (tail->end_marker != 0x16) {
        printf("警告: 无效帧尾 (0x%02X != 0x16)\n", tail->end_marker);
        return false;
    }

    // 计算并验证校验和
    size_t checksum_data_length = packet_length - WAVE_TAIL_SIZE - 1; // 校验和计算的数据长度
    const uint8_t* data_ptr = packet_data + 1; // 从命令符开始
    uint32_t calculated_checksum = CalculateChecksum(data_ptr, checksum_data_length);

    printf("校验和检查: 计算值=0x%08X, 接收值=0x%08X\n", calculated_checksum, tail->checksum);

    if (calculated_checksum != tail->checksum) {
        printf("错误: 校验和不匹配!\n");
        return false;
    }
    printf("校验和验证通过 (0x%08X)\n", calculated_checksum);



    // 计算周波数据数量
    size_t available_data = packet_length - WAVE_HEADER_SIZE - WAVE_TAIL_SIZE;
    size_t expected_cycle_size = sizeof(hal_data_t);
    int num_cycles = available_data / expected_cycle_size;

    printf("可用数据长度: %zu 字节, 单个周波大小: %zu 字节\n", available_data, expected_cycle_size);
    printf("预计周波数量: %d\n", num_cycles);

    if (num_cycles <= 0) {
        printf("错误: 数据不足，无法解析周波数据\n");
        return false;
    }

    // 解析每个周波数据
    uint32_t sequences[20]; // 最多20个周波
    uint32_t seq_count = 0;
    size_t data_offset = WAVE_HEADER_SIZE;

    for (int i = 0; i < num_cycles && i < 20; i++) {
        if (data_offset + sizeof(hal_data_t) > packet_length - WAVE_TAIL_SIZE) {
            printf("警告: 数据不足，无法解析所有周波 (已解析 %d/%d)\n", i, num_cycles);
            break;
        }

        hal_data_t* cycle = (hal_data_t*)(packet_data + data_offset);
        data_offset += sizeof(hal_data_t);

        // 记录序号
        sequences[seq_count++] = cycle->cycle_seq;

        // 解析时间信息
        std::string current_time = GetTimeamp((uint8_t*)&cycle->year);

        printf("\n=== 周波 #%d ===\n", i + 1);
        printf("周波序号: %u, 时间: %s\n", cycle->cycle_seq, current_time.c_str());

        // 解析采样数据
        const char* signal_names[] = {"A相电压", "B相电压", "C相电压", "A相电流", "B相电流", "C相电流", "零线电流"};
        const size_t SAMPLES_PER_CHANNEL = 256;  // 每个通道256个采样点

        // 创建波形采样数据结构
        WaveformSample sample;
        sample.timestamp = GetTimestampFromTimeString(current_time);

        // 检查周波序号是否发生改变
        if (cycle->cycle_seq == m_lastCycleSeq) {
            printf("周波序号未改变 (%u)，跳过数据处理\n", cycle->cycle_seq);
            continue;  // 跳过这个周波，继续处理下一个
        }

        m_lastCycleSeq = cycle->cycle_seq;
        printf("周波序号已改变 (%u)，开始处理新波形: %s\n", cycle->cycle_seq, current_time.c_str());

        // 用于存储每个通道的256个采样点
        std::vector<std::vector<float>> channel_samples(7);
        for (int ch = 0; ch < 7; ch++) {
            channel_samples[ch].reserve(SAMPLES_PER_CHANNEL);
        }

        CalculateUrms(channel_samples);
    }

    // 检查序号连续性
    if (seq_count > 1) {
        printf("\n=== 序号连续性检查 ===\n");
        for (uint32_t i = 1; i < seq_count; i++) {
            if (sequences[i] != sequences[i-1] + 1) {
                printf("警告: 序号不连续! 序号[%u]=%u, 序号[%u]=%u\n",
                       i-1, sequences[i-1], i, sequences[i]);
            }
        }
        printf("序号检查完成，共检查%u个序号\n", seq_count);
    }

    // 执行暂态事件判断（不进行数据存储）
    printf("\n=== 开始暂态事件判断 ===\n");
    JudgeTansientEvent();
    printf("暂态事件判断完成\n");


    return true;
}

bool CTransientManager::ReadWaveformData()
{
    if (!m_pBuffer) {
        printf("错误：环形缓冲区未初始化\n");
        return false;
    }

    // 获取缓冲区中的所有波形数据
    std::vector<WaveformSample> samples;
    if (!m_pBuffer->GetAllSamples(samples)) {
        printf("警告：缓冲区为空或读取失败\n");
        return false;
    }

    printf("\n=== 缓冲区波形数据读取 ===\n");
    printf("缓冲区大小: %zu 个波形样本\n", samples.size());
    printf("时间戳格式: 微秒时间戳\n");
    printf("数据格式: [时间戳] A相电压(V) B相电压(V) C相电压(V) A相电流(A) B相电流(A) C相电流(A)\n");
    printf("========================================\n");

    // 按时间顺序打印每个波形样本
    for (size_t i = 0; i < samples.size(); ++i) {
        const WaveformSample& sample = samples[i];

        // 转换时间戳为可读格式（可选）
        time_t timestamp_sec = sample.timestamp / 1000000;  // 转换为秒
        uint32_t microsec = sample.timestamp % 1000000;     // 微秒部分

        struct tm* timeinfo = localtime(&timestamp_sec);
        char time_str[64];
        strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", timeinfo);

        printf("[%zu] [%s.%06u] ", i + 1, time_str, microsec);

        // 打印ABC相电压
        printf("VA:%.3f VB:%.3f VC:%.3f ",
               sample.channels[0], sample.channels[1], sample.channels[2]);

        // 打印ABC相电流
        printf("IA:%.3f IB:%.3f IC:%.3f\n",
               sample.channels[3], sample.channels[4], sample.channels[5]);
    }

    printf("========================================\n");

   

    return true;

}


std::string CTransientManager::GetTimeamp(const uint8_t *data)
{

    int year = 2000 + data[0];  
    int month = data[1];        
    int day = data[2];          
    int hour = data[3];        
    int minute = data[4];       
    int second = data[5];       
    uint32_t microsecond = data[6] | (data[7] << 8) | (data[8] << 16) | (data[9] << 24);

    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%04d/%02d/%02d, %02d:%02d:%02d.%06u",
             year, month, day, hour, minute, second, microsecond);

    return std::string(buffer);
}

uint64_t CTransientManager::GetTimestampFromTimeString(const std::string& time_str)
{
    int year, month, day, hour, minute, second;
    uint32_t microsecond;

    int parsed = sscanf(time_str.c_str(), "%d/%d/%d, %d:%d:%d.%u",
                       &year, &month, &day, &hour, &minute, &second, &microsecond);

    if (parsed != 7) {
        // 解析失败，返回当前时间的微秒时间戳
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    }

    // 转换为time_t
    struct tm tm_time = {};
    tm_time.tm_year = year - 1900;  // tm_year是从1900年开始的年数
    tm_time.tm_mon = month - 1;     // tm_mon是0-11
    tm_time.tm_mday = day;
    tm_time.tm_hour = hour;
    tm_time.tm_min = minute;
    tm_time.tm_sec = second;
    tm_time.tm_isdst = -1;          // 让系统决定是否为夏令时

    time_t timestamp = mktime(&tm_time);
    if (timestamp == -1) {
        // mktime失败，返回当前时间
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
    }

    // 转换为微秒时间戳
    uint64_t microsecond_timestamp = static_cast<uint64_t>(timestamp) * 1000000ULL + microsecond;

    return microsecond_timestamp;
}

int32_t CTransientManager::ConvertToInt32(const uint8_t* data)
{
    // 将4个字节的数据转换为32位有符号整数（小端序）
    int32_t value = data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24);
    return value;
}

uint32_t CTransientManager::CalculateChecksum(const uint8_t* data, size_t length)
{
    // 累加校验和算法
    uint32_t checksum = 0;
    for (size_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return checksum;
}

float CTransientManager::ConvertToFloat(const uint8_t* data)
{
    // 将4个字节的数据转换为float（小端序）
    float value;
    std::memcpy(&value, data, 4);

    // 检查是否为有效的浮点数
    if (std::isnan(value) || std::isinf(value)) {
        printf("Warning: Invalid float value detected, raw bytes: 0x%02x%02x%02x%02x\n",
               data[0], data[1], data[2], data[3]);
        return 0.0f;
    }

    return value;
}

// 周波数据计算
bool CTransientManager::CalculateUrms(const std::vector<std::vector<float>>& channel_samples)
{
    printf("=== 计算三相电压RMS值 ===\n");

    float va_rms_sum = 0, vb_rms_sum = 0, vc_rms_sum = 0;

    if (!channel_samples[0].empty()) {
        for (float val : channel_samples[0]) va_rms_sum += val * val;
        va_urms = sqrt(va_rms_sum / channel_samples[0].size());
        printf("A相电压RMS: %.6f V\n", va_urms);
    }

    if (!channel_samples[1].empty()) {
        for (float val : channel_samples[1]) vb_rms_sum += val * val;
        vb_urms = sqrt(vb_rms_sum / channel_samples[1].size());
        printf("B相电压RMS: %.6f V\n", vb_urms);
    }

    if (!channel_samples[2].empty()) {
        for (float val : channel_samples[2]) vc_rms_sum += val * val;
        vc_urms = sqrt(vc_rms_sum / channel_samples[2].size());
        printf("C相电压RMS: %.6f V\n", vc_urms);
    }

    printf("三相电压RMS值计算完成，准备进行事件判断\n");
    printf("==================\n");
    return true;
}


//判断发生事件
bool CTransientManager::JudgeTansientEvent()
{
    // 获取参数值
     float voltage = CParamManager::CreateInstance().m_Param_value["std_voltage"];
    float SwlStrVal_Lim = CParamManager::CreateInstance().m_Param_value["SwlStrVal_Lim"] * voltage;    // 暂升定值
    float DipStrVal_Lim = CParamManager::CreateInstance().m_Param_value["DipStrVal_Lim"] * voltage;    // 暂降定值
    float IntrStrVal_Lim = CParamManager::CreateInstance().m_Param_value["IntrStrVal_Lim"] * voltage;  // 短时中断定值
   
    //float DalayV = CParamManager::CreateInstance().m_Param_value["DalayVoltage"];
    float DalayV = 0.0f;
    cout<<"短时中断定值"<<IntrStrVal_Lim<<endl;
    cout<<"暂升定值"<<SwlStrVal_Lim<<endl;
    cout<<"暂降定值"<<DipStrVal_Lim<<endl;
    cout<<"标准电压"<<voltage<<endl;
    cout<<"电压越限值"<<DalayV<<endl;

    // 获取当前时间戳函数
    // auto getCurrentTimeMillis = []() {
    //     return std::chrono::duration_cast<std::chrono::milliseconds>(
    //         std::chrono::system_clock::now().time_since_epoch()
    //     ).count();
    // };

    // if (m_CurrentEvent.empty()) // 没有正在进行的事件
    // {
    //     if (va_urms < IntrStrVal_Lim || vb_urms < IntrStrVal_Lim || vc_urms < IntrStrVal_Lim)  //满足告警定值
    //     {
    //         // 记录事件开始时间戳
    //         m_EventStartTime = getCurrentTimeMillis();
    //         m_CurrentEvent = "IntrStr_Alm";
    //         m_EventSampleCount = 5;
    //         m_EventSampleCount++;
    //         cout<<"短时中断事件"<<endl;
    //         cout<<m_EventSampleCount<<endl;
    //     }
    //     else if ((va_urms >= SwlStrVal_Lim && va_urms < 1.1f * voltage) || (vb_urms >= SwlStrVal_Lim && vb_urms < 1.1f * voltage) || (vc_urms >= SwlStrVal_Lim && vc_urms < 1.1f * voltage))
    //     {
    //         // 记录事件开始时间戳 
    //         m_EventStartTime = getCurrentTimeMillis();
    //         m_CurrentEvent = "SwlStr_Alm";
    //         m_EventSampleCount = 5;
    //         m_EventSampleCount++;
    //         cout<<"暂升事件"<<endl;
    //         cout<<m_EventSampleCount<<endl;
    //     }
    //     else if ((va_urms <= DipStrVal_Lim && va_urms > IntrStrVal_Lim) || (vb_urms <= DipStrVal_Lim && vb_urms > IntrStrVal_Lim) || (vc_urms <= DipStrVal_Lim && vc_urms > IntrStrVal_Lim))
    //     {
    //         // 记录事件开始时间
    //         m_EventStartTime = getCurrentTimeMillis();
    //         m_CurrentEvent = "DipStr_Alm";
    //         m_EventSampleCount = 5; 
    //         m_EventSampleCount++;
    //         cout<<"暂降事件"<<endl;
    //         cout<<m_EventSampleCount<<endl;
    //     }
    // }
    // else // 有正在进行的事件
    // {
    //     if (m_CurrentEvent == "IntrStr_Alm") // 短时中断事件处理
    //     {
    //         if (va_urms >= IntrStrVal_Lim + DalayV && vb_urms >= IntrStrVal_Lim + DalayV && vc_urms >= IntrStrVal_Lim + DalayV)  //满足复归条件
    //         {
    //             // 记录事件结束时间
    //             m_EventEndTime = getCurrentTimeMillis();
    //         }
    //     }
    //     else if (m_CurrentEvent == "SwlStr_Alm") // 暂升事件处理
    //     {
    //         if (va_urms < SwlStrVal_Lim && vb_urms < SwlStrVal_Lim && vc_urms < SwlStrVal_Lim)  //满足复归条件
    //         {
    //             // 记录事件结束时间
    //             m_EventEndTime = getCurrentTimeMillis();
    //         }
    //     }
    //     else if (m_CurrentEvent == "DipStr_Alm") // 暂降事件处理
    //     {
    //         if (va_urms > DipStrVal_Lim && vb_urms > DipStrVal_Lim && vc_urms > DipStrVal_Lim)  //满足复归条件
    //         {
    //             // 记录事件结束时间
    //             m_EventEndTime = getCurrentTimeMillis();
    //         }
    //     }
    // }

    //暂升
    if(va_urms>=(CParamManager::CreateInstance().m_Param_value["SwlStrVal_Lim"]*220) || vb_urms>=(CParamManager::CreateInstance().m_Param_value["SwlStrVal_Lim"]*220) || vc_urms >=(CParamManager::CreateInstance().m_Param_value["SwlStrVal_Lim"]*220))
    {
        SWL_Alm_Tm_s += 10;   

    }else{
        SWL_Alm_Tm_s = 0;  
    }
    //暂降  
    if(va_urms<=(CParamManager::CreateInstance().m_Param_value["DipStrVal_Lim"]*220) || vb_urms<=(CParamManager::CreateInstance().m_Param_value["DipStrVal_Lim"]*220) || vc_urms <=(CParamManager::CreateInstance().m_Param_value["DipStrVal_Lim"]*220))
    {
        DIP_Alm_Tm_s += 10;

    }else{
        DIP_Alm_Tm_s = 0;
    }
    //中断
    if(va_urms<=(CParamManager::CreateInstance().m_Param_value["IntrStrVal_Lim"]*220) || vb_urms<=(CParamManager::CreateInstance().m_Param_value["IntrStrVal_Lim"]*220) || vc_urms <=(CParamManager::CreateInstance().m_Param_value["IntrStrVal_Lim"]*220))
    {
        INTR_Alm_Tm_s += 10;

    }else{
        INTR_Alm_Tm_s = 0;
    }
    JudgeTansientEvent_YX();
    return true;
}

bool CTransientManager::JudgeTansientEvent_YX()
{
    // 计算事件持续时间
    // float event_duration = (m_EventEndTime - m_EventStartTime) / 1000.0f + 5.0f;  
    // if(CParamManager::CreateInstance().m_Param_value["IntrStrVal_Enable"] > 0.5)
    // {  
    //     if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] == 0)
    //     {
    //         // 事件持续时间有效，发送遥信
    //         CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] = 1;
    //         CTaskManager::CreateInstance().MqttPackNotificationData_2("IntrStrVal");
    //         IEC_LOG_RECORD(eRunType,"短时中断 IntrStrVal :%d", CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"]);
    //         m_EventEndTime = 0;
    //         m_EventStartTime = 0;
    //         cout<<"事件持续时间"<<event_duration<<endl;
    //         cout<<"遥信事件"<<CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"]<<endl;
    //     }
    // }
    // if(CParamManager::CreateInstance().m_Param_value["SwlStrVal_Enable"] > 0.5)
    // {  
    //     if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"] == 0)
    //     {
    //         // 事件持续时间有效，发送遥信
    //         CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"] = 1;
    //         CTaskManager::CreateInstance().MqttPackNotificationData_2("SwlStrVal");
    //         m_EventEndTime = 0;
    //         m_EventStartTime = 0;
    //     }
    // }
    // if(CParamManager::CreateInstance().m_Param_value["DipStrVal_Enable"] > 0.5)
    // {  
    //     if (event_duration >= 0.01f && event_duration <= 60.0f && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] == 0)
    //     {
    //         // 事件持续时间有效，发送遥信
    //         CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] = 1;
    //         CTaskManager::CreateInstance().MqttPackNotificationData_2("DipStrVal");
    //         m_EventEndTime = 0;
    //         m_EventStartTime = 0;
    //     }
    // }
    if(CParamManager::CreateInstance().m_Param_value["DipStrVal_Enable"] > 0.5)
    {    
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (DIP_Alm_Tm_s >= CParamManager::CreateInstance().m_Param_value["DipStrVal_Dly"] && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] == 0)
        {
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] = 1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("DipStrVal");
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件
        if(DIP_Alm_Tm_s == 0 && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStr_Alm"] == 1)
        {
             CParamManager::CreateInstance().m_pdAnalyzer_value_YX["DipStrVal"] = 0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("DipStr_Alm");
        }     
    }
    //电压暂升
    if(CParamManager::CreateInstance().m_Param_value["SwlStrVal_Enable"] > 0.5)
    {
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件
        if (SWL_Alm_Tm_s >= CParamManager::CreateInstance().m_Param_value["SwlStrVal_Dly"] && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"] == 0)
        {
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"]=1;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SwlStrVal");
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件
        if(SWL_Alm_Tm_s == 0 && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"] == 1)
        {
             CParamManager::CreateInstance().m_pdAnalyzer_value_YX["SwlStrVal"]=0;
            //发送遥信
            CTaskManager::CreateInstance().MqttPackNotificationData_2("SwlStrVal");
        }     
    }
    //电压中断
    //mskprintf("INTR_Enable:%f\n",m_Param_value["INTR_Enable"]);
    if(CParamManager::CreateInstance().m_Param_value["IntrStrVal_Enable"] > 0.5)
    { 
        // mskprintf("--------time:%d\n",m_voltage_time.INTR_Alm_Tm_s);
        // mskprintf("INTR_Dly:%f\n",m_Param_value["INTR_Dly"]);
        // mskprintf("IntrStr_Alm:%d\n",m_pdAnalyzer_value_YX["IntrStr_Alm"]);
        // Alm: 过压时间大于定值 并且上一次事件为0  报遥信事件 
        //电压短时中断的时间内不报告警事件，电压恢复上电时立即上报告警事件
        if (INTR_Alm_Tm_s >= CParamManager::CreateInstance().m_Param_value["IntrStrVal_Dly"] && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] == 0)
        {
            CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] = 1;
            IEC_LOG_RECORD(eRunType,"中断 IntrStr_Alm :%d", CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"]);
            //发送遥信
            //CTaskManager::CreateInstance().MqttPackNotificationData_2("IntrStr_Alm");
            
        }
        // Alm: 过压时间为0 并且上一次事件为1  报遥信事件 
        if(INTR_Alm_Tm_s == 0 && CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] == 1)
        {
            //m_pdAnalyzer_value_YX["I1ntrStr_Alm"] = 1;
            //发送遥信
            IEC_LOG_RECORD(eRunType,"中断 IntrStr_Alm :%d", CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"]);
            CTaskManager::CreateInstance().MqttPackNotificationData_2("IntrStrVal");
             CParamManager::CreateInstance().m_pdAnalyzer_value_YX["IntrStrVal"] = 0;
        }     
    }

    return true;
}


