#include"asn1.h"

using namespace LINECAP;
using namespace asn1;

asn_basic::asn_basic(const asn_basic& src)
{
    PRM  = src.PRM;
    PRIORITY = src.PRIORITY;
    INDEX = src.INDEX;  
    LABEL = src.LABEL; 
    SOURCE = src.SOURCE;        
    DESTINATION = src.DESTINATION;   
    MsgTag.IID = src.MsgTag.IID;        
    MsgTag.IOP = src.MsgTag.IOP;
    MSG_Length = src.MSG_Length;
    MSG_Payload.append(src.MSG_Payload); 

}
asn_basic& asn_basic::operator=(const asn_basic& src)
{
    PRM  = src.PRM;
    PRIORITY = src.PRIORITY;
    INDEX = src.INDEX;  
    LABEL = src.LABEL; 
    SOURCE = src.SOURCE;        
    DESTINATION = src.DESTINATION;   
    MsgTag.IID = src.MsgTag.IID;        
    MsgTag.IOP = src.MsgTag.IOP;
    MSG_Length = src.MSG_Length;
    MSG_Payload.append(src.MSG_Payload); 

	return *this;
}

linecap_buff asn_basic::serialize()
{
    linecap_buff buff;
    char str_end = 0;
    LINECAP_BUFF_APPEND(buff, &PRM, sizeof(PRM));
    LINECAP_BUFF_APPEND(buff, &PRIORITY, sizeof(PRIORITY));
    LINECAP_BUFF_APPEND(buff, &INDEX, sizeof(INDEX));
    LINECAP_BUFF_APPEND(buff, &LABEL, sizeof(LABEL)); 
    LINECAP_BUFF_APPEND(buff, SOURCE.data(), SOURCE.size());
    LINECAP_BUFF_APPEND(buff, &str_end, sizeof(str_end));
    LINECAP_BUFF_APPEND(buff, DESTINATION.data(), DESTINATION.size());
    LINECAP_BUFF_APPEND(buff, &str_end, sizeof(str_end));
    LINECAP_BUFF_APPEND(buff, &MsgTag.IOP, sizeof(MsgTag.IOP));
    LINECAP_BUFF_APPEND(buff, &MsgTag.IID, sizeof(MsgTag.IID));
    LINECAP_BUFF_APPEND(buff, &MSG_Length, sizeof(MSG_Length));
    buff.append(MSG_Payload);
    return std::move(buff);
}

int fund_name_end(size_t pos,size_t &string_end,const linecap_buff& src)
{
    int count = 0;
    for (string_end = pos; string_end < src.size(); string_end++) {
        if (src.const_data()[string_end] == 0) return 0;
    }
    return 1;

}
bool asn_basic::unserialize(const linecap_buff& src)
{

    if (src.size() == 0){
		return false;
	}
	uint32_t offset = 0;
	PRM = src.get(offset, sizeof(PRM)).value<char>();
	offset += sizeof(PRM);
	PRIORITY = src.get(offset, sizeof(PRIORITY)).value<char>();
	offset += sizeof(PRIORITY);
	INDEX = src.get(offset, sizeof(INDEX)).value<char>();
	offset += sizeof(INDEX);
    LABEL = src.get(offset, sizeof(LABEL)).value<uint16_t>();
	offset += sizeof(LABEL);
    
    size_t  end;
    char * name ;
    if(!fund_name_end(offset,end,src))
    SOURCE = src.get(offset, end-offset).to_string();
    else 
    {
        printf("unfund SOURCE string end ");
        return false;
    }
    offset = end+1;
    if(!fund_name_end(offset,end,src))
        DESTINATION = src.get(offset, end-offset).to_string();
    else 
    {
        printf("unfund DESTINATION string end ");
        return false;
    }

    offset = end+1;
    MsgTag.IOP = src.get(offset, sizeof(MsgTag.IOP)).value<uint16_t>();
    offset += sizeof(MsgTag.IOP);
    MsgTag.IID = src.get(offset, sizeof(MsgTag.IID)).value<uint16_t>();
    offset += sizeof(MsgTag.IID);
    MSG_Length = src.get(offset, sizeof(MSG_Length)).value<char>();
    offset += sizeof(MSG_Length);
    MSG_Payload = src.get(offset, MSG_Length);
    return true;
}

bool asn_basic::unserialize(char*srcs,int len)
{
    linecap_buff src;
    src.append(srcs,len);
    if (src.size() == 0){
		return false;
	}
	uint32_t offset = 0;
	PRM = src.get(offset, sizeof(PRM)).value<char>();
	offset += sizeof(PRM);
	PRIORITY = src.get(offset, sizeof(PRIORITY)).value<char>();
	offset += sizeof(PRIORITY);
	INDEX = src.get(offset, sizeof(INDEX)).value<char>();
	offset += sizeof(INDEX);
    LABEL = src.get(offset, sizeof(LABEL)).value<uint16_t>();
	offset += sizeof(LABEL);
    size_t  end;
    if(!fund_name_end(offset,end,src))
    SOURCE = src.get(offset, end-offset).to_string();
    else 
    {
        printf("unfund SOURCE string end ");
        return false;
    }
    offset = end+1;
    if(!fund_name_end(offset,end,src))
    DESTINATION = src.get(offset, end-offset).to_string();
    else 
    {
        printf("unfund DESTINATION string end ");
        return false;
    }
    offset = end+1;
    MsgTag.IOP = src.get(offset, sizeof(MsgTag.IOP)).value<uint16_t>();
    offset += sizeof(MsgTag.IOP);
    MsgTag.IID = src.get(offset, sizeof(MsgTag.IID)).value<uint16_t>();
    offset += sizeof(MsgTag.IID);
    MSG_Length = src.get(offset, sizeof(MSG_Length)).value<char>();
    offset += sizeof(MSG_Length);
    MSG_Payload = src.get(offset, MSG_Length);

    printf("asn1.cpp SOURCE: %s ",SOURCE.c_str());
    return true;
}

linecap_buff DIINFO::encoder()  //封装好 后面直接追加
{
    linecap_buff buff;
    LINECAP_BUFF_APPEND(buff, data_no, sizeof(int));
    LINECAP_BUFF_APPEND(buff, &attributeid, sizeof(attributeid));
    return std::move(buff);
}
bool DIINFO::decoder(const linecap_buff& src)
{
    if (src.size() == 0){
		return false;
	}
    uint32_t offset = 0;   
    LINECAP_BUFF_SET_VALUE(&data_no, src.const_data() + offset,sizeof(int), offset);
    LINECAP_BUFF_SET_VALUE(&attributeid, src.const_data() + offset,sizeof(attributeid), offset);
    size = offset;
    return true;
}

linecap_buff data::encoder()
{
    linecap_buff buff; 
    buff.append(DATA_info.encoder()); //直接追加
    LINECAP_BUFF_APPEND(buff, &dataclassid, sizeof(dataclassid));//填充数据内容
    char len = databuffer.size(); 
    LINECAP_BUFF_APPEND(buff, &len, sizeof(char)); //变长内容 增加长度位置
    buff.append(databuffer);
    return std::move(buff);
}
bool data::decoder(const linecap_buff& src)
{
    if (src.size() == 0){
		return false;
	}
    uint32_t offset = 0;   
    DATA_info.decoder(src);
    offset+=DATA_info.size;
    dataclassid=src.get(offset, sizeof(dataclassid)).value<short>();
	offset += sizeof(dataclassid);
    char len = src.get(offset, sizeof(len)).value<char>();
    offset += sizeof(len);
    databuffer.append(src.const_data()+offset,len);
    size = offset + len;
    return true;

}
int data::buff_to_value()
{
    int value  = 0;
    //  databuffer.const_data();
    linecap_buff buff;
    buff.append(databuffer);
    LINECAP_BUFF_APPEND(buff, &value, sizeof(char));
    memcpy(&value, databuffer.const_data(), 4);
    printf("电压值 is %d",value);

}

linecap_buff write_data::encoder()
{

    linecap_buff buff; 
    LINECAP_BUFF_APPEND(buff, &log_dev_number, sizeof(log_dev_number));//填充数据内容
    LINECAP_BUFF_APPEND(buff, &information_number, sizeof(information_number));//填充数据内容
    char len = dataset.size(); //有几个结构体数据
    buff.append(&len,sizeof(char));//填充结构体个数
    buff.append(get_dataset());//填充内容数据
    return std::move(buff);

}
bool write_data::decoder(const linecap_buff& src)
{
    if (src.size() == 0){
		return false;
	}
    uint32_t offset = 0;   
    log_dev_number=src.get(offset, sizeof(log_dev_number)).value<char>();
	offset += sizeof(log_dev_number);
    information_number=src.get(offset, sizeof(information_number)).value<short>();
	offset += sizeof(information_number);
    unsigned char len = src.get(offset, sizeof(len)).value<char>();
    offset+= sizeof(char);
    for(unsigned char lens = 0;lens<len;lens++)
    {   
        linecap_buff buff;
        buff.append(src.const_data()+offset,src.size()-offset);
        data da;
        da.decoder(buff);
        dataset.push_back(da);
        offset+= da.size;
    }

    return true;

}

bool write_data_ack::decoder(const linecap_buff& src)
{
     if (src.size() == 0){
		return false;
	}
	uint32_t offset = 0;
    char len =src.get(offset, sizeof(len)).value<char>();
	offset += sizeof(len);
    station = src.get(offset, sizeof(station)).value<char>();
    return station;
}
linecap_buff write_data::get_dataset()
{
    linecap_buff buff; 
    auto first = dataset.begin();
    auto end = dataset.end();
    while (first != end)
    {
        buff.append(first->encoder());
        ++first;
    }
    return std::move(buff);

}

linecap_buff data::deal_vt(asn1::DIINFO &fo,char flage,double value)
{
    int n = vt ;
    // char *ptr = (char *)fo.data_no;
    memcpy( fo.data_no, &n, sizeof(int) );
    fo.attributeid = flage;
    char *ptrs = (char *)&value;
    linecap_buff buff;
    LINECAP_BUFF_APPEND(buff, ptrs, sizeof(value));
    return std::move(buff);

}

linecap_buff data::deal_data(asn1::DIINFO &fo,int type,char flage,int value)
{
    // int n = vt ;
    // voltage;
    // char *ptr = (char *)fo.data_no;
    memcpy(fo.data_no, &type, sizeof(int) );
    fo.attributeid = flage;
    char *ptrs = (char *)&value;
    linecap_buff buff;
    LINECAP_BUFF_APPEND(buff, ptrs, sizeof(value));
    return std::move(buff);

}
linecap_buff data::deal_vt(asn1::DIINFO &fo,char flage,int value)
{
    int n = vt ;
    // char *ptr = (char *)fo.data_no;
    memcpy( fo.data_no, &n, sizeof(int) );
    fo.attributeid = flage;
    char *ptrs = (char *)&value;
    linecap_buff buff;
    LINECAP_BUFF_APPEND(buff, ptrs, sizeof(value));
    return std::move(buff);

}
//对普通数据打包
void data::asn1_send_pack_data(asn1::data &da_s,asn1::DIINFO fo,unsigned char dataclassid,linecap_buff datas)
{
    memcpy(da_s.DATA_info.data_no, fo.data_no,sizeof(int) );
    da_s.DATA_info.attributeid = fo.attributeid;
    da_s.dataclassid = dataclassid;
    da_s.databuffer.append(datas);
}
//组包写普通数据
void asn1_send_data(write_data & data)
{
    data.log_dev_number = acMeter;
    data.information_number = 7;
    // asn1_esnd_pack_data(data.dataset);

}


