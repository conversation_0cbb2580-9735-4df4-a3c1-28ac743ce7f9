/*
*********************************************************************
* Copyright(C) 2018 China Electric Power Research Institute
* All rights reserved.
* @brief：   基础数据类型定义.
* @date：    2018/09/10
* @history：
*********************************************************************
*/

#ifndef __DATA_TYPE_H__
#define __DATA_TYPE_H__

#ifdef WIN32
#include <windows.h>
#include <process.h>
#endif

#ifdef __cplusplus
extern "C"
{
#endif

#undef bool
#undef uchar
#undef uint8
#undef int8
#undef uint16
#undef int16
#undef uint32
#undef int32
#undef uint64
#undef int64
#undef float32
#undef float64

#ifndef bool
#define bool int
#endif

#ifndef uchar
#define uchar unsigned char
#endif

#ifndef uint8
#define uint8 unsigned char
#endif

#ifndef int8
#define int8 char
#endif

#ifndef uint16
#define uint16 unsigned short
#endif

#ifndef int16
#define int16 short
#endif

#ifndef uint32
#define uint32 unsigned int
#endif

#ifndef int32
#define int32 int
#endif

#ifndef uint64
#define uint64 unsigned long long
#endif

#ifndef int64
#define int64 long long
#endif

#ifndef float32
#define float32 float
#endif

#ifndef float64
#define float64 double
#endif

#ifndef HANDLE
#define HANDLE void *
#endif

#ifndef NULL
#define NULL 0
#endif

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

#ifndef INFINITE
#define INFINITE (0xFFFFFFFF)
#endif

#ifndef WIN32
#define __noop ((void)0)
#endif

	typedef union
	{
		unsigned char uc[4];
		unsigned int ul;
	} varul_t;

	typedef union
	{
		unsigned char uc[2];
		unsigned short us;
	} varus_t;

#ifdef __cplusplus
}
#endif

#endif // __DATA_TYPE_H__
