/**
*********************************************************************
* @copyright Copyright(C) 2018 China Electric Power Research Institute
* All rights reserved.
* @file    hal.h
* @brief  HAL框架基础数据结构定义
* @note
* @warning  无
* @date    05/10/2019
*********************************************************************
*/

#ifndef __HAL_H__
#define __HAL_H__

#include "common.h"
// #include "dataType.h"

#ifdef __cplusplus
extern "C"
{
#endif

#define HW_MODULE_MAXDEVICES (256)

    struct tag_HW_DEVICE;
    struct tag_HW_MODULE;

    typedef struct tag_HW_MODULE
    {
        const char *szName;      /**< 模块名 */
        uint32 nVer;             /**< 模块版本号 */
        const char *szAuthor;    /**< 作者 */
        uint32 nFactory;         /**< 厂商代码 */
        const char *szBuildDate; /**< 发布日期YYYY-MM-DD */
        uint32 nDevNum;          /**< 硬件设备数 */
        /** 模块包含的硬件设备集合 */
        char **szDevices;

        /**
         * @brief 打开设备访问接口
         * @param[in] pModule: 接口模块对象指针
         * @param[in] szDeviceID: 设备 ID
         * @param[out] ppDevice: 设备访问接口对象指针
         * @return 成功返回 ERR_OK；失败返回错误码。
         */
        int32 (*pfOpen)(struct tag_HW_MODULE *pModule,
                        const char *szDeviceID, struct tag_HW_DEVICE **ppDevice);
        /**
         * @brief 关闭设备访问接口
         * @param[in] pModule: 接口模块对象指针
         * @param[in] pDevice: 设备访问接口对象指针
         * @return 成功返回 ERR_OK；失败返回错误码。
         */
        int32 (*pfClose)(struct tag_HW_MODULE *pModule,
                         struct tag_HW_DEVICE *pDevice);
    } HW_MODULE;

    typedef struct tag_HW_DEVICE
    {
        struct tag_HW_MODULE *pModule; /**< 设备所属模块对象指针 */
        int32 nVer;                    /**< 设备接口版本号 */
        const char *szDeviceID;        /**< 设备ID名 */
    } HW_DEVICE;

    /**
    *********************************************************************
    * @brief       hal库初始化
    * @param[in]   无
    * @param[out]  无
    * @return      成功返回0; 失败返回小于0错误编号
    * @note        函数的应用编程手册（详细描述函数的使用说明）
    * @warning     注意事项
    *********************************************************************
    */
    int32 hal_init(void);

    /**
    *********************************************************************
    * @brief       获取设备驱动
    * @param[in]  device_id: 设备名称
    * @param[out]  无
    * @return      成功返回设备操作函数接口; 失败返回NULL
    * @note        函数的应用编程手册（详细描述函数的使用说明）
    * @warning     注意事项
    *********************************************************************
    */
    struct tag_HW_DEVICE *hal_device_get(const char *device_id);

    /**
    *********************************************************************
    * @brief       关闭设备并释放设备驱动
    * @param[in]  dev: 设备指针
    * @param[out]  无
    * @return      成功返回ERR_OK; 失败返回错误编号
    * @note        函数的应用编程手册（详细描述函数的使用说明）
    * @warning     注意事项
    *********************************************************************
    */
    int32 hal_device_release(struct tag_HW_DEVICE *dev);

    /**
    *********************************************************************
    * @brief       驱动库退出
    * @param[in]  无
    * @param[out]  无
    * @return      成功返回0; 失败返回小于0错误编号
    * @note        函数的应用编程手册（详细描述函数的使用说明）
    * @warning     注意事项
    *********************************************************************
    */
    int32 hal_exit(void);

#ifdef __cplusplus
}
#endif

#endif // __HAL_H__
