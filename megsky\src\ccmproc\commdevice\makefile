##############################################################################
# Copyright (c) 2020 山东梅格彤天电气有限公司 http://www.megsky.com
#
# @file    : Makefile
# @brief   : STD library Makefile
# @note
#
# 
##############################################################################

TZCDIR			 = ../../..
APPNAME			 = commdevice
CROSS_COMPILE	?= aarch64-linux-gnu-
TARGET  		?= $(TZCDIR)/lib/libcommdevice.so 


CC 				:= $(CROSS_COMPILE)g++

INCDIRS		:=  $(TZCDIR)/src/include \
				$(TZCDIR)/src/include/tzc_std \
				$(TZCDIR)/src/include/meg_pub \
				$(TZCDIR)/src/include/commdevice \
				
SRCDIRS		:=  $(TZCDIR)/src/ccmproc/$(APPNAME) \
				

INCLUDE		:= $(patsubst %, -I %, $(INCDIRS))

CPPFILES	:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.cpp))
CPPFILENDIR	:= $(notdir  $(CPPFILES))
CPPOBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CPPFILENDIR:.cpp=.o))

CFILES		:= $(foreach dir, $(SRCDIRS), $(wildcard $(dir)/*.c))
CFILENDIR	:= $(notdir  $(CFILES))
COBJS		:= $(patsubst %, $(TZCDIR)/obj/$(APPNAME)/%, $(CFILENDIR:.c=.o))

OBJS		:= $(CPPOBJS) $(COBJS)

CFLAGS  	:= -fPIC -shared $(INCLUDE) $(ADDED_CFLAGS) 
LDFLAGS		:=-L$(TZCDIR)/lib -L$(TZCDIR)/lib/third_lib -L/usr/lib -lpthread -ltzcstd -lmegpub -lnsl

VPATH		:= $(SRCDIRS)

.PHONY : clean packet


$(TARGET) : $(OBJS)
	$(CC) $(CFLAGS) $(OBJS) $(LDFLAGS) -o $@ 

$(CPPOBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.cpp
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

$(COBJS):$(TZCDIR)/obj/$(APPNAME)/%.o: %.c
	$(CC) -c $(CFLAGS) $(INCLUDE) $< -o $@ 

pac :
	objdump -d $(APPNAME) > MM.s
	
clean :
	rm -rf $(OBJS)
	rm -rf $(TARGET)
