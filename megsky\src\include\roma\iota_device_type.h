/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012.
 */

#ifndef INC_AGENT_IOTA_DEVICE_TYPE_H
#define INC_AGENT_IOTA_DEVICE_TYPE_H

#include "hw_type.h"

// extend info max num
#define MAX_EXTENDINFO_NUM 10
typedef struct stru_ST_IOTA_DEVICE_EXTEND_INFO
{
   HW_CHAR *key;
   HW_CHAR *value;
} ST_IOTA_DEVICE_EXTEND_INFO;

/**
 * <b>Description:</b> It is used to notify caller login successed. 
 * <br><b>Purpose:</b> After the caller login, When the UI receives this notification, it means the caller 
 * login successed, and the UI must be updated. The UI can not receive this notification if caller login 
 * failed. The UI is recommended to perform interception on this notification during the entire running 
 * of the application.
 */
typedef struct stru_ST_IOTA_DEVICE_INFO
{
    HW_CHAR *pcDeviceId;         /* Optional */
    HW_CHAR *pcNodeId;           /* required */
    HW_CHAR *pcNodeType; 
    HW_CHAR *pcName;             /* required */
    HW_CHAR *pcDescription;      /* Optional */
    HW_CHAR *pcProductId;
    HW_CHAR *pcManufacturerId;   /* required */
    HW_CHAR *pcManufacturerName; 
    HW_CHAR *pcMac;
    HW_CHAR *pcLocation;
    HW_CHAR *pcDeviceType;
    HW_CHAR *pcModel;            /* required */
    HW_CHAR *pcSwVersion;
    HW_CHAR *pcFwVersion;
    HW_CHAR *pcHwVersion;
    HW_CHAR *pcProtocolType;
    HW_CHAR *pcBridgeId;
    HW_CHAR *pcStatus;
    HW_CHAR *pcStatusDetail;
    HW_CHAR *pcMute;
    ST_IOTA_DEVICE_EXTEND_INFO pcExtendInfo[MAX_EXTENDINFO_NUM]; // extend info MAX_NUM = 10
} ST_IOTA_DEVICE_INFO;


#endif

