/*=====================================================================
 * 文件：public_struct.h
 *
 * 描述：公共定义的头文件定义
 *
 * 作者：田振超			2021年5月14日13:54:32
 * 
 * 修改记录：
 =====================================================================*/

#ifndef PUBLIC_STRUCT_H
#define PUBLIC_STRUCT_H

#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>
#include <signal.h>
#include <semaphore.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include "pub_std.h"
#include <errno.h>

#ifdef __cplusplus
extern "C" {
#endif

/* MQTT订阅发布相关 */
#define QOS         0
#define TIMEOUT     10000L
#define MSG_ARRVD_MAX_LEN  20 * 16 * 1024
#define PATH_MAX 1024

const Juint32	QUEUE_LEN = 8192;

#define TCP_CLIENT_T 0
#define TCP_SERVER_T 1

void printf_enable(bool b);
bool get_printf_enable();
void mskprintf(const char * format, ...);
int memcpy_safe(void *s1, int s1_size, void *s2, int s2_size, int n);
void LogWrite(ELogType eLogType, const char * format, ...);

#define CFG_USING_LOG
#define CFG_USING_LOG_COLOR

#ifdef CFG_USING_LOG

#ifdef CFG_USING_LOG_COLOR
#define MG_DEBUG(fmt, ...) mskprintf(fmt, ##__VA_ARGS__)
#define MG_PRINT(fmt, ...) mskprintf(fmt, ##__VA_ARGS__)
#define MG_LOG(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#define MG_LOG_I(fmt, ...) mskprintf("\033[32;40m" fmt "\033[0m \n", ##__VA_ARGS__)
#define MG_LOG_D(fmt, ...) mskprintf("\033[33;40m" fmt "\033[0m \n", ##__VA_ARGS__)
#define MG_LOG_E(fmt, ...) mskprintf("\033[31;40m" fmt "\033[0m \n", ##__VA_ARGS__)
#else
#define MG_DEBUG(fmt, ...) mskprintf(fmt, ##__VA_ARGS__)
#define MG_PRINT(fmt, ...) mskprintf(fmt, ##__VA_ARGS__)
#define MG_LOG(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#define MG_LOG_I(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#define MG_LOG_D(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#define MG_LOG_E(fmt, ...) mskprintf(fmt " \n", ##__VA_ARGS__)
#endif

#else
#define MG_DEBUG(fmt, ...)
#define MG_PRINT(fmt, ...)
#define MG_LOG(fmt, ...)
#define MG_LOG_I(fmt, ...)
#define MG_LOG_D(fmt, ...)
#define MG_LOG_E(fmt, ...)

#endif

//宏实现(对象类名)
#define IEC_LOG_RECORD(logtype, format, arg...) LogWrite(logtype,format, ##arg);

#define IEC_ASSERT(EXPR)                                                   \
    if (!(EXPR))                                                           \
    {                                                                      \
        IEC_LOG_RECORD(eErrType, "(%s) has assert failed at %s,line:%d\n", \
                       #EXPR, __FILE__, __LINE__);                         \
        return;                                                            \
    }
/* 基本数据类型和常量定义 */
#define TOKEN_RELEASE 0
#define TOKEN_SHUT 1


#define MQTT_OK 0
#define MQTT_ERR 1
#define VOS_OK 0
#define VOS_ERR 1
#define DEVICE_DELETE       2
#define DEVICE_ONLINE       1
#define DEVICE_OFFLINE      0

#define DEV_INFO_MSG_BUFFER_LEN 1024
#define DATA_BUF_F256_SIZE 256
#define SG_KEEP_ALIVE_INTERVAL (60)
#define MQTT_DISCONNECT_TIMEOUT 10000
//#define FILE_FRAME_SIZE 2048

#define LOG_FILE_SIZE 2 //单位MB

#define DATACENTREVERSION "2.1"
#define COMPATIBLEVERSION "1.0"

/* 此APP名称 */
#define CFG_APP_NAME "DTAnalyzer"

/* 断路器名称 */
#define MCCB_NAME "SwitchCollect"

/* 电表名称 */
#define METER "Meter"


// 打印控制开关
#define IEC_PRINT_FILEPATH "/data/app/"CFG_APP_NAME"/enable.print"
#define IEC_CFG_PATH    "/data/app/"CFG_APP_NAME"/"
#define IEC_EXCFG_PATH    "/data/app/"CFG_APP_NAME"/"

#define IEC_MQTT_FILENAME "mqttIot_config.json"
#define IEC_TASKRECORD_FILENAME "taskRecord.json"

/* 配置文件路径 */

#define IEC_MQTTFILE_FILEPATH IEC_CFG_PATH IEC_MQTT_FILENAME
#define IEC_TASKRECORD_FILEPATH IEC_CFG_PATH IEC_TASKRECORD_FILENAME

//#define DEVICE_INFO_FILEPATH  "../configFile/pqAnalyzer_info.json"
//#define DEVICE_INFO_FILEPATH_ABS  "/data/app/" CFG_APP_NAME "/configFile/pqAnalyzer_info.json"

#define DEVICE_INFO_FILEPATH  "../configFile/"CFG_APP_NAME"Config.json"
#define DEVICE_INFO_FILEPATH_ABS  "/data/app/" CFG_APP_NAME "/configFile/"CFG_APP_NAME"Config.json"

#define PRE_DATA_INFO_FILEPATH_ABS  "/data/app/" CFG_APP_NAME "/configFile/predata.json"

#define COMTRADE_PATH "data/app/"CFG_APP_NAME"commFile/COMTRADE"


//#define DATABASE_PATH "/mnt/nand/model.db"
#define DATABASE_PATH IEC_EXCFG_PATH "model.db"
/* APP日志存储路径 */

//#define IEC_LOG_PATH IEC_EXCFG_PATH "logFile/"CFG_APP_NAME
#define IEC_LOG_PATH  "/run/log/DTAnalyzer/DTAnalyzer"

#define IEC_HISTORY_PATH "/data/app/"CFG_APP_NAME"/"


/* 数据中心APP名称 */
#define CFG_DATA_CENTER_NAME "dataCenter"
#define CFG_OS_NAME "smiOS"

/* 设备重启延时时间，表示在多少秒之后重启，取值范围1~60s */
#define CFG_RST_DELAY 1

#define FLAG_NULL   0
#define FLAG_FAULT  1
#define FLAG_RECOVER  2

#define QUERY_PARAM   0
#define QUERY_DD  1
#define QUERY_YCYXOCT  2

#define EVENT_MORMAL   0
#define EVENT_NULL   1

#define REGISTER   0
#define UNREGISTER   1



/*Start================电能质量分析定义=======================*/

#define     SEC_PER_MIN     30

/*Start================MQTT定义=======================*/

/* MQTT消息通用部分结构体 */
typedef struct mqtt_header
{
    char        token[40];
    char        timestamp[32];
}mqtt_header_s;

typedef struct mqtt_data_info
{
    Juint32 msg_send_lenth;
	char msg_send[MSG_ARRVD_MAX_LEN];
    char pubtopic[256];
    int  retained;                  // 可保留的
}mqtt_data_info_s;

/*End================设备信息定义=======================*/

#define F_DESC(x) 1
#define SEM_MAX_WAIT_TIMES 20
#define MAX_QUE_DEPTH 4096
#define MSECOND 1000000
#define TEN_MSECOND 100000
#define REQUEST_TYPE 77
#define JSON_BUF_SIZE 256

typedef unsigned char method_t; //方法类型

//请求报文头
typedef struct mqtt_request_header
{
	Jint32              mid;
    std::string         deviceId;        
    std::string         timestamp;        
    Jint32              expire;        
    std::string         type;   
    /* param */         
}mqtt_request_header_s;

//应答报文头
typedef struct mqtt_reply_header
{
	Jint32              mid;
    std::string         deviceId;        
    std::string         timestamp;       
    std::string         type;  
    /* param */
    Juint16             code;
    std::string         msg;
}mqtt_reply_header_s;

// 模型内容
typedef struct model_content
{
    Juint32             id;
    std::string         name;
    std::string         type;
    std::string         unit;
    std::string         deadzone;
    std::string         ratio;
    std::string         isReport;
    std::string         userdefine;
}model_content_s;

// 设备内容
typedef struct dev_content
{
    Juint32             id;
    std::string         model;  // 拼接
    std::string         port;  // 拼接
    std::string         addr;  // 拼接
    std::string         desc;  // 拼接
    std::string         manuID;
    std::string         manuName;
    std::string         ProType;
    std::string         deviceType;
    std::string         isReport;
    std::string         nodeID;
    std::string         productID;
    std::string         guid;       //拼接产生guid
}dev_content_s;

// 参数内容
typedef struct dev_param
{
    Juint32             id;
    std::string         name;
    std::string         val;
    std::string         unit;
    std::string         datatype;
}dev_param_s;


// GUID内容
typedef struct guid_body
{
    std::string         dev;
    std::string         guid;
}guid_body_s;

//定值参数
typedef struct det_stat_lim_para
{
    float   PowerOff_V_Lim;   //失压/停电告警定值
    float   PowerOff_Dly;   //失压/停电告警时间
    float   PowerOn_V_Lim;   //有压/来电告警定值
    float   PowerOn_Dly;   //有压/来电告警时间
    float   PTUA_Dly;       //失流判定时间
    float   PTUA_Loss_Dly;   //断流判定时间   
			
    float   PTUV_Lim;   //电压偏移（低压）告警定值            
    float   PTUV_Dly;   //电压偏移（低压）告警时间          
    float   PTOV_Lim;   //电压偏移（过压）告警定值        
    float   PTOV_Dly;   //电压偏移（过压）告警时间      
    float   Load_Lim;   //负荷越限告警定值               
    float   Load_Dly;   //负荷越限告警时间               
    float   PTOC_Hvld_Lim;   //配变重载告警定值               
    float   PTOC_Hvld_Dly;   //配变重载告警时间               
    float   PTOC_Ovld_Lim;   //配变过载告警定值        
    float   PTOC_Ovld_Dly;   //配变过载告警时间   
    float   deviceLoad;         //配变容量
    float   std_voltage;            //额定电压 220V
    float   std_Hz;                 //额定频率 50Hz
    float   Imb_db;            //相电流不平衡告警启动条件 0.4
    float   ImbNgA_phsN_Lim;           //零线电流不平衡度限值 0.25
    float   PhvImb_strVal;          //电压不平衡度限值
    float   LoadImb_strVal;          //负荷不平衡度限值 
       
}det_stat_lim_para_s;

//交采采集数据
typedef struct jc_run_data
{
    float   PhV_phsA;//A相电压
    float   PhV_phsB;//B相电压
    float   PhV_phsC;//C相电压
    float   SeqV_c0;
    float   A_phsA;//A相电流
    float   A_phsB;//B相电流
    float   A_phsC;//C相电流 
    float   SeqA_c0;
    float   Hz;
    float   PhPF_phsA;      //A 相功率因数
    float   PhPF_phsB;
    float   PhPF_phsC;
    float   TotPF;          //总功率因数

    float   PhVA_phsA;  //A 相视在功率
    float   PhVA_phsB;
    float   PhVA_phsC;
    float   TotVA;      //总视在功率

}jc_run_data_s;

//时间统计
typedef struct voltage_det_stat
{    
    float   PTOV_Tm_phsA_S;//1分钟内的越线时间  单位秒  用于计时 
    float   PTUV_Tm_phsA_S;
    float   PASS_Tm_phsA_S;
    float   PTOV_Tm_phsB_S;//1分钟内的越线时间  单位秒  用于计时 
    float   PTUV_Tm_phsB_S;
    float   PASS_Tm_phsB_S;
    float   PTOV_Tm_phsC_S;//1分钟内的越线时间  单位秒  用于计时 
    float   PTUV_Tm_phsC_S;
    float   PASS_Tm_phsC_S;

    float   PhV_Tm_Day_phsA; //当日A 相电压监测时间   单位分钟
    float   PTOV_Tm_Day_phsA;//当日A 相电压超上限时间   单位分钟
    float   PTUV_Tm_Day_phsA;//当日A 相电压超下限时间   单位分钟
    float   PASS_Tm_Day_phsA;//当日A 相电压合格时间   单位分钟

    float   PhV_Tm_Day_phsB; //当日B 相电压监测时间   单位分钟
    float   PTOV_Tm_Day_phsB;//当日B 相电压超上限时间   单位分钟
    float   PTUV_Tm_Day_phsB;//当日B 相电压超下限时间   单位分钟
    float   PASS_Tm_Day_phsB;//当日B 相电压合格时间   单位分钟

    float   PhV_Tm_Day_phsC; //当日C 相电压监测时间   单位分钟
    float   PTOV_Tm_Day_phsC;//当日C 相电压超上限时间   单位分钟
    float   PTUV_Tm_Day_phsC;//当日C 相电压超下限时间   单位分钟
    float   PASS_Tm_Day_phsC;//当日C 相电压合格时间   单位分钟

    float   PhV_Tm_Mon_phsA; //当月A 相电压监测时间   单位分钟
    float   PTOV_Tm_Mon_phsA;//当月A 相电压超上限时间   单位分钟
    float   PTUV_Tm_Mon_phsA;//当月A 相电压超下限时间   单位分钟
    float   PASS_Tm_Mon_phsA;//当月A 相电压合格时间   单位分钟

    float   PhV_Tm_Mon_phsB; //当月B 相电压监测时间   单位分钟
    float   PTOV_Tm_Mon_phsB;//当月B 相电压超上限时间   单位分钟
    float   PTUV_Tm_Mon_phsB;//当月B 相电压超下限时间   单位分钟
    float   PASS_Tm_Mon_phsB;//当月B 相电压合格时间   单位分钟

    float   PhV_Tm_Mon_phsC; //当月C 相电压监测时间   单位分钟
    float   PTOV_Tm_Mon_phsC;//当月C 相电压超上限时间   单位分钟
    float   PTUV_Tm_Mon_phsC;//当月C 相电压超下限时间   单位分钟
    float   PASS_Tm_Mon_phsC;//当月C 相电压合格时间   单位分钟

    float   ImbNgV_Tm_Day;   //三相电压不平衡日累计时间  单位分钟
    float   ImbNgV_Tm_Mon;   //三相电压不平衡月累计时间  单位分钟
    float   ImbNgA_Tm_Day;   //三相电流不平衡日累计时间  单位分钟
    float   ImbNgA_Tm_Mon;   //三相电流不平衡月累计时间  单位分钟
    
    float ImbNgV_Num_Day;    //三相电压不平衡日累计次数 
    float ImbNgV_Num_Mon;    //三相电压不平衡月累计次数
    float ImbNgA_Num_Day;    //三相电流不平衡日累计次数
    float ImbNgA_Num_Mon;    //三相电流不平衡月累计次数 单位无

    float   PTUC_lvld_Tm_Day;               //轻载日累计时间
    float   PTUC_lvld_Tm_Alm;               //轻载告警持续时长
    float   PTOC_Hvld_Tm_Day;               //重载日累计时间
    float   Re_PTOC_Hvld_Tm_Day;            //反向重载日累计时间
    float   PTOC_Hvld_Tm_Alm;               //重载告警持续时长
    float   Re_PTOC_Hvld_Tm_Alm;            //反向重载告警持续时长
    float   PTOC_mild_Ovld_Tm_Day;          //正向轻度过载日累计时间
    float   Re_PTOC_mild_Ovld_Tm_Day;       //反向轻度过载日累计时间
    float   PTOC_mild_Ovld_Tm_Alm;          //正向轻度过载告警持续时长
    float   Re_PTOC_mild_Ovld_Tm_Alm;       //反向轻度过载告警持续时长
    float   PTOC_middle_Ovld_Tm_Day;        //正向中度过载日累计时间
    float   Re_PTOC_middle_Ovld_Tm_Day;     //反向中度过载日累计时间
    float   PTOC_middle_Ovld_Tm_Alm;        //正向中度过载告警持续时长
    float   Re_PTOC_middle_Ovld_Tm_Alm;     //反向中度过载告警持续时长  
    float   PTOC_severe_Ovld_Tm_Day;        //正向重度过载日累计时间
    float   Re_PTOC_severe_Ovld_Tm_Day;     //反向重度过载日累计时间
    float   PTOC_severe_Ovld_Tm_Alm;        //正向重度过载告警持续时长
    float   Re_PTOC_severe_Ovld_Tm_Alm;     //反向重度过载告警持续时长
    float   PTOC_abnormal_Ovld_Tm_Day;      //正向异常过载日累计时间
    float   Re_PTOC_abnormal_Ovld_Tm_Day;   //反向异常过载日累计时间
    float   PTOC_abnormal_Ovld_Tm_Alm;      //正向异常过载告警持续时长
    float   Re_PTOC_abnormal_Ovld_Tm_Alm;   //反向异常过载告警持续时长
    float   PTUC_Unld_Tm_Day;               //空载日累计时间
    float   PTUC_Unld_Tm_Alm;               //空载告警持续时长

    float   ThdPhV_Tm_Day;          //电压总谐波日越限次数
    float   ThdPhV_Tm_Mon;          //电压总谐波月越限次数
    float   ThdA_Tm_Day;            //电流总谐波日越限次数
    float   ThdA_Tm_Mon;            //电流总谐波月越限次数
    float   PF_Tm_Day;              //功率因素日越限次数
    float   PF_Tm_Mon;              //功率因素月越限次数
    
    
    //遥信时间统计
    int   PTOV_Op_phsA_Tm_s;  //A相连续过压时间，单位 秒
    int   PTOV_Op_phsB_Tm_s;  //B相连续过压时间，单位 秒
    int   PTOV_Op_phsC_Tm_s;  //C相连续过压时间，单位 秒
    int   PTOV_Alm_Tm_s;      //过压告警时间，单位 秒   A/B/C 有一个过压，即过压。
    int   PTOV_FG_Op_phsA_Tm_s;  //A相连续过压复归时间，单位 秒
    int   PTOV_FG_Op_phsB_Tm_s;  //B相连续过压复归时间，单位 秒
    int   PTOV_FG_Op_phsC_Tm_s;  //C相连续过压复归时间，单位 秒
    int   PTOV_FG_Alm_Tm_s;      //过压复归告警时间，单位 秒   

    int   SeqOV_Alm_Tm_s;      //零序过压告警时间，单位 秒 

    int   PTUV_Op_phsA_Tm_s;  //A相连续低压时间，单位 秒
    int   PTUV_Op_phsB_Tm_s;  //B相连续低压时间，单位 秒
    int   PTUV_Op_phsC_Tm_s;  //C相连续低压时间，单位 秒
    int   PTUV_Alm_Tm_s;      //低压告警时间，单位 秒   A/B/C 有一个低压，即低压。 
    int   PTUV_FG_Op_phsA_Tm_s;  //A相连续低压复归时间，单位 秒
    int   PTUV_FG_Op_phsB_Tm_s;  //B相连续低压复归时间，单位 秒
    int   PTUV_FG_Op_phsC_Tm_s;  //C相连续低压复归时间，单位 秒
    int   PTUV_FG_Alm_Tm_s;      //低压复归告警时间，单位 秒   

    int   PTUV_Loss_Op_phsA_Tm_s;  //A相连续矢压/停电时间，单位 秒
    int   PTUV_Loss_Op_phsB_Tm_s;  //B相连续矢压/停电时间，单位 秒
    int   PTUV_Loss_Op_phsC_Tm_s;  //C相连续矢压/停电时间，单位 秒
    int   PTUV_Loss_Alm_Tm_s;      //矢压/停电告警时间，单位 秒   A&B&C 全部矢压，即矢压。

    int   PWR_Off_Op_phsA_Tm_s;    //A相连续停电时间，单位秒
    int   PWR_Off_Op_phsB_Tm_s;    //A相连续停电时间，单位秒
    int   PWR_Off_Op_phsC_Tm_s;    //A相连续停电时间，单位秒
    int   PowerOff_Tm_s;           //停电告警时间，单位秒   


    int   PowerOn_phsA_Tm_s;    //A相连续有压/来电时间，单位 秒
    int   PowerOn_phsB_Tm_s;    //B相连续有压/来电时间，单位 秒
    int   PowerOn_phsC_Tm_s;    //C相连续有压/来电时间，单位 秒
    int   PowerOn_Alm_Tm_s;     //有压/来电告警时间，单位 秒   A&B&C 全部有压，即有压。


    int   PTUV_Open_phsA_Tm_s;    //A相断相时间，单位 秒
    int   PTUV_Open_phsB_Tm_s;    //B相断相时间，单位 秒
    int   PTUV_Open_phsC_Tm_s;    //C相断相时间，单位 秒
    int   PTUV_Open_Alm_Tm_s;     //断相告警时间，单位 秒   A/B/C 任意一个断相 即断相。

    int   PhsSeqV_Alm_Tm_s;     //电压逆向序。


    int   PTUC_Op_phsA_Tm_s;    //A相失流时间，单位 秒
    int   PTUC_Op_phsB_Tm_s;    //B相失流时间，单位 秒
    int   PTUC_Op_phsC_Tm_s;    //C相失流时间，单位 秒
    int   PTUC_Op_Alm_Tm_s;     //失流告警时间，单位 秒   A/B/C 一个/两个小于启动电流，其他大于启动电流， 即失流。


    int   PTUC_Open_Op_phsA_Tm_s;    //A相断流时间，单位 秒
    int   PTUC_Open_Op_phsB_Tm_s;    //B相断流时间，单位 秒
    int   PTUC_Open_Op_phsC_Tm_s;    //C相断流时间，单位 秒
    int   PTUC_Open_Op_Alm_Tm_s;     //断流告警时间，单位 秒   A/B/C 任意一个断流 即断断流。


    int   PTOA_Op_phsA_Tm_s;  //A相连续过流时间，单位 秒
    int   PTOA_Op_phsB_Tm_s;  //B相连续过流时间，单位 秒
    int   PTOA_Op_phsC_Tm_s;  //C相连续过流时间，单位 秒
    int   PTOA_Alm_Tm_s;      //过流告警时间，单位 秒   A/B/C 有一个过流，即过流。 

    int   SeqOC_Alm_Tm_s;      //零序过流告警时间，单位 秒 

    int   Ovld_phsA_Tm_s;  //A相连续负荷越线时间，单位 秒
    int   Ovld_phsB_Tm_s;  //B相连续负荷越线时间，单位 秒
    int   Ovld_phsC_Tm_s;  //C相连续负荷越线时间，单位 秒
    int   Ovld_Alm_Tm_s;      //负荷越线告警时间，单位 秒   A/B/C 有一个负荷越线，即负荷越线。 


    int   PTOC_Hvld_phsA_Tm_s;      //A相连续配变重载越线时间，单位 秒
    int   PTOC_Hvld_phsB_Tm_s;      //B相连续配变重载越线时间，单位 秒
    int   PTOC_Hvld_phsC_Tm_s;      //C相连续配变重载越线时间，单位 秒
    int   PTOC_Hvld_Alm_Tm_s;       //配变重载越线告警时间，单位 秒   A/B/C 有一个配变重载越线，即配变重载越线。 

    int   Re_PTOC_Hvld_phsA_Tm_s;      //A相连续配变反向重载越线时间，单位 秒 
    int   Re_PTOC_Hvld_phsB_Tm_s;      //B相连续配变反向重载越线时间，单位 秒
    int   Re_PTOC_Hvld_phsC_Tm_s;      //C相连续配变反向重载越线时间，单位 秒 
    int   Re_PTOC_Hvld_Alm_Tm_s;       //配变反向重载越线告警时间，单位 秒   A/B/C 有一个配变重载越线，即配变重载越线。 

    int   PTOC_Ovld_phsA_Tm_s;      //A相连续配变过载越线时间，单位 秒
    int   PTOC_Ovld_phsB_Tm_s;      //B相连续配变过载越线时间，单位 秒
    int   PTOC_Ovld_phsC_Tm_s;      //C相连续配变过载越线时间，单位 秒
    int   PTOC_Ovld_Alm_Tm_s;       //配变过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。 

    int   PTOC_Unld_phsA_Tm_s;      //A相连续配变空载越线时间，单位 秒
    int   PTOC_Unld_phsB_Tm_s;      //B相连续配变空载越线时间，单位 秒
    int   PTOC_Unld_phsC_Tm_s;      //C相连续配变空载越线时间，单位 秒
    int   PTUC_Unld_Alm_Tm_s;       //配变空载越线告警时间，单位 秒   A/B/C 有一个配变空载越线，即配变空载越线。
    
    int   PTOC_lvld_phsA_Tm_s;      //A相连续配变轻载越线时间，单位 秒
    int   PTOC_lvld_phsB_Tm_s;      //B相连续配变轻载越线时间，单位 秒
    int   PTOC_lvld_phsC_Tm_s;      //C相连续配变轻载越线时间，单位 秒
    int   PTUC_lvld_Alm_Tm_s;       //配变轻载越线告警时间，单位 秒   A/B/C 有一个配变轻载越线，即配变轻载越线。

    int   PTOC_mild_Ovld_phsA_Tm_s;      //A相连续配变轻度过载越线时间，单位 秒
    int   PTOC_mild_Ovld_phsB_Tm_s;      //B相连续配变轻度过载越线时间，单位 秒
    int   PTOC_mild_Ovld_phsC_Tm_s;      //C相连续配变轻度过载越线时间，单位 秒
    int   PTOC_mild_Ovld_Alm_Tm_s;       //配变轻度过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。

    int   Re_PTOC_mild_Ovld_phsA_Tm_s;      //A相连续配变轻度过载越线时间，单位 秒
    int   Re_PTOC_mild_Ovld_phsB_Tm_s;      //B相连续配变轻度过载越线时间，单位 秒
    int   Re_PTOC_mild_Ovld_phsC_Tm_s;      //C相连续配变轻度过载越线时间，单位 秒
    int   Re_PTOC_mild_Ovld_Alm_Tm_s;       //配变轻度过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。

    int   PTOC_middle_Ovld_phsA_Tm_s;      //A相连续配变中度过载越线时间，单位 秒
    int   PTOC_middle_Ovld_phsB_Tm_s;      //B相连续配变中度过载越线时间，单位 秒
    int   PTOC_middle_Ovld_phsC_Tm_s;      //C相连续配变中度过载越线时间，单位 秒
    int   PTOC_middle_Ovld_Alm_Tm_s;       //配变中度过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。

    int   Re_PTOC_middle_Ovld_phsA_Tm_s;      //A相连续配变中度过载越线时间，单位 秒
    int   Re_PTOC_middle_Ovld_phsB_Tm_s;      //B相连续配变中度过载越线时间，单位 秒
    int   Re_PTOC_middle_Ovld_phsC_Tm_s;      //C相连续配变中度过载越线时间，单位 秒
    int   Re_PTOC_middle_Ovld_Alm_Tm_s;       //配变中度过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。

    int   PTOC_severe_Ovld_phsA_Tm_s;      //A相连续配变重度过载越线时间，单位 秒
    int   PTOC_severe_Ovld_phsB_Tm_s;      //B相连续配变重度过载越线时间，单位 秒
    int   PTOC_severe_Ovld_phsC_Tm_s;      //C相连续配变重度过载越线时间，单位 秒
    int   PTOC_severe_Ovld_Alm_Tm_s;       //配变重度过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。

    int   Re_PTOC_severe_Ovld_phsA_Tm_s;      //A相连续配变重度过载越线时间，单位 秒
    int   Re_PTOC_severe_Ovld_phsB_Tm_s;      //B相连续配变重度过载越线时间，单位 秒
    int   Re_PTOC_severe_Ovld_phsC_Tm_s;      //C相连续配变重度过载越线时间，单位 秒
    int   Re_PTOC_severe_Ovld_Alm_Tm_s;       //配变重度过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。

    int   PTOC_abnormal_Ovld_phsA_Tm_s;      //A相连续配变异常过载越线时间，单位 秒
    int   PTOC_abnormal_Ovld_phsB_Tm_s;      //B相连续配变异常过载越线时间，单位 秒
    int   PTOC_abnormal_Ovld_phsC_Tm_s;      //C相连续配变异常过载越线时间，单位 秒
    int   PTOC_abnormal_Ovld_Alm_Tm_s;       //配变异常过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。

    int   Re_PTOC_abnormal_Ovld_phsA_Tm_s;      //A相连续配变异常过载越线时间，单位 秒
    int   Re_PTOC_abnormal_Ovld_phsB_Tm_s;      //B相连续配变异常过载越线时间，单位 秒
    int   Re_PTOC_abnormal_Ovld_phsC_Tm_s;      //C相连续配变异常过载越线时间，单位 秒
    int   Re_PTOC_abnormal_Ovld_Alm_Tm_s;       //配变异常过载越线告警时间，单位 秒   A/B/C 有一个配变过载越线，即配变过载越线。



    int   Res_Alm_Tm_s;         //剩余电流超限时间，单位 秒
    int   Res_FG_Tm_s;         //剩余电流复归时间，单位 秒

    int   PTUPF_phsA_Tm_s;       //功率因数超限时间，单位 秒
    int   PTUPF_phsB_Tm_s;       //功率因数超限时间，单位 秒
    int   PTUPF_phsC_Tm_s;       //功率因数超限时间，单位 秒
    int   PTUPF_Alm_Tm_s;        //功率因数超限时间，单位 秒
    int   PTUPF_phsA_FG_Tm_s;       //功率因数复归时间，单位 秒
    int   PTUPF_phsB_FG_Tm_s;       //功率因数复归时间，单位 秒
    int   PTUPF_phsC_FG_Tm_s;       //功率因数复归时间，单位 秒
    int   PTUPF_Alm_FG_Tm_s;        //功率因数复归时间，单位 秒
    
    


    int   DIP_Alm_Tm_s;//电压暂降超限时间 单位 秒
    int   SWL_Alm_Tm_s;//电压暂升超限时间 单位 秒
    int   INTR_Alm_Tm_s;//电压中断超限时间 单位 秒

    int   ThdPhV_Op_V_Tm_s;       //电压谐波越限时间，单位 秒
    int   ThdPhV_Op_PhsA_Tm_s;    //A相电压谐波越限时间，单位 秒
    int   ThdPhV_Op_PhsB_Tm_s;    //B相电压谐波越限时间，单位 秒
    int   ThdPhV_Op_PhsC_Tm_s;    //C相电压谐波越限时间，单位 秒

    int   ThdA_Op_A_Tm_s;       //电流谐波越限时间，单位 秒
    int   ThdA_Op_PhsA_Tm_s;    //A相电流谐波越限时间，单位 秒
    int   ThdA_Op_PhsB_Tm_s;    //B相电流谐波越限时间，单位 秒
    int   ThdA_Op_PhsC_Tm_s;    //C相电流谐波越限时间，单位 秒

    int   PTOF_Op_Tm_s;        //过频时间 单位 秒
    int   PTOF_Op_phsA_Tm_s;   //A相过频时间 单位 秒
    int   PTOF_Op_phsB_Tm_s;   //B相过频时间 单位 秒
    int   PTOF_Op_phsC_Tm_s;   //C相过频时间 单位 秒
    int   PTUF_Op_Tm_s;        //欠频时间 单位 秒
    int   PTUF_Op_phsA_Tm_s;   //A相欠频时间 单位 秒
    int   PTUF_Op_phsB_Tm_s;   //B相欠频时间 单位 秒
    int   PTUF_Op_phsC_Tm_s;   //C相欠频时间 单位 秒

    int   PhaseA_Break_L1_Tm_s;  //高压侧A相断相级别1持续时间
    int   PhaseA_Break_L2_Tm_s;  //高压侧A相断相级别2持续时间
    int   PhaseB_Break_L1_Tm_s;  //高压侧A相断相级别1持续时间
    int   PhaseB_Break_L2_Tm_s;  //高压侧A相断相级别1持续时间
    int   PhaseC_Break_L1_Tm_s;  //高压侧A相断相级别1持续时间
    int   PhaseC_Break_L2_Tm_s;  //高压侧A相断相级别1持续时间
    
}voltage_det_stat_s;


//模型item
typedef struct model_item
{ 
    std::string  name;  
    std::string  type;  
    std::string  unit;  
    std::string  deadzone;
    std::string  ratio;  
    std::string  isReport;
    std::string  userdefine;  
}model_item_s;

typedef struct deviceValue
{
    std::string deviceGuid;
    voltage_det_stat_s m_device_time;
    
};

int folder_mkdirs(const char *folder_path);

int copyFile(std::string ors, std::string dst);

#ifdef __cplusplus
}
#endif

#endif

