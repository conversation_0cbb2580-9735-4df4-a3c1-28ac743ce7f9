#ifndef __HAL_UART_H__
#define __HAL_UART_H__

#ifdef __cplusplus
extern "C"
{
#endif
#include "hal.h"
#include "dataType.h"
#define ADAPT_BASE_APP 0

#if ADAPT_BASE_APP
#define MAX_PATH 256
#define UART_REMOTE_CU_DEVICE_ID_STR "uart_remote_cu"
#define UART_LOCAL_CU_DEVICE_ID_STR "uart_local_cu"
#define UART_USB_AT_DEVICE_ID_STR "uart_usb_at"
#define UART_USB_DIAG_DEVICE_ID_STR "uart_usb_diag"
#define UART_YX_DEVICE_ID_STR "uart_acm_yx"
#endif
#define UART_RS4851_DEVICE_ID_STR "uart_rs485_1"/* 4851串口  */
#define UART_RS4852_DEVICE_ID_STR "uart_rs485_2"/* 4852串口  */
#define UART18_DEVICE_ID_STR "uart_acm1_0"              /* 模块1的管理串口*/
#define UART19_DEVICE_ID_STR "uart_acm1_1" /* 模块1的串口1 */
#define UART20_DEVICE_ID_STR "uart_acm1_2" /* 模块1的串口2 */
#define UART21_DEVICE_ID_STR "uart_acm1_3" /* 模块1的串口3 */
#define UART22_DEVICE_ID_STR "uart_acm1_4" /* 模块1的串口4 */
#define UART23_DEVICE_ID_STR "uart_acm2_0" /* 模块2的管理串口 */
#define UART24_DEVICE_ID_STR "uart_acm2_1" /* 模块2的串口1 */
#define UART25_DEVICE_ID_STR "uart_acm2_2" /* 模块2的串口2 */
#define UART26_DEVICE_ID_STR "uart_acm2_3" /* 模块2的串口3 */
#define UART27_DEVICE_ID_STR "uart_acm2_4" /* 模块2的串口4 */
#define UART28_DEVICE_ID_STR "uart_acm3_0" /* 模块3的管理串口 */
#define UART29_DEVICE_ID_STR "uart_acm3_1" /* 模块3的串口1 */
#define UART30_DEVICE_ID_STR "uart_acm3_2" /* 模块3的串口2 */
#define UART31_DEVICE_ID_STR "uart_acm3_3" /* 模块3的串口3 */
#define UART32_DEVICE_ID_STR "uart_acm3_4" /* 模块3的串口4 */
#define UART33_DEVICE_ID_STR "uart_acm4_0" /* 模块4的管理串口 */
#define UART34_DEVICE_ID_STR "uart_acm4_1" /* 模块4的串口1 */
#define UART35_DEVICE_ID_STR "uart_acm4_2" /* 模块4的串口2 */
#define UART36_DEVICE_ID_STR "uart_acm4_3" /* 模块4的串口3 */
#define UART37_DEVICE_ID_STR "uart_acm4_4" /* 模块4的串口4 */
#define UART38_DEVICE_ID_STR "uart_acm5_0" /* 模块5的管理串口 */
#define UART39_DEVICE_ID_STR "uart_acm5_1" /* 模块5的串口1 */
#define UART40_DEVICE_ID_STR "uart_acm5_2" /* 模块5的串口2 */
#define UART41_DEVICE_ID_STR "uart_acm5_3" /* 模块5的串口3 */
#define UART42_DEVICE_ID_STR "uart_acm5_4" /* 模块5的串口4 */

#define HW_DEVICE_ID_UART 				"uart"
#define UART_DEV_PATH_SIZE 				512

/* 校验位 */
typedef enum
{
	PARITY_NO = 0,	// 无校验
	PARITY_ODD,		// 奇校验
	PARITY_EVEN		// 偶校验
} PARITY_TYPE_E;

typedef struct tag_UART_DEVICE
{
	struct tag_HW_DEVICE base;

	/**
	 * @brief 设置串口通信参数
	 * @param[in] dev: 设备描述
	 * @param[in] baud 波特率
	 * @param[in] databits 数据位(5~8)
	 * @param[in] stopbits 停止位(1~2)
	 * @param[in] parity 校验位(N 无校验， E 偶校验， O 奇校验)
	 * @return 成功返回 ERR_OK，失败返回错误码。
	 */
	int32 (*uart_param_set)(struct tag_UART_DEVICE *dev, uint32 baud, uint32 databits,
							uint32 stopbits, PARITY_TYPE_E parity);

	/**
	 * @brief 从串口接收数据
	 * @param[in] dev: 设备描述
	 * @param[out] buf 接收缓存区
	 * @param[in] len 缓存区长度
	 * @return 成功返回数据长度，失败返回错误码
	 */
	int32 (*uart_data_recv)(struct tag_UART_DEVICE *dev, uint8 *buf, uint32 len);

	/**
	 * @brief 通过串口发送数据
	 * @param[in] dev: 设备描述
	 * @param[in] buf 发送缓存区
	 * @param[in] len 缓存区长度
	 * @return 成功返回ERR_OK，失败返回错误码
	 */
	int32 (*uart_data_send)(struct tag_UART_DEVICE *dev, const uint8 *buf, int32 len);

	/**
	 * @brief 获取串口句柄
	 * @param[in] dev: 设备描述
	 * @return返回串口打开时的句柄值（正常>0，异常<=0）。
	 */
	int32 (*uart_get_handle)(struct tag_UART_DEVICE *dev);

	/**
	 * * @brief 获取串口真实的设备节点名
	 * * @param[in] dev: 设备描述
	 * * @param[out] dev_name: 设备节点名
	 * * @return 返回获取到的设备节点名的长度。
	 * */
	int32 (*uart_get_real_dev_name)(struct tag_UART_DEVICE *dev, uint8 *dev_name);
	/**
	 * * @brief 获取串口状态
	 * * @param[in] dev: 设备描述
	 * * @return 返回串口当前状态（正常>=0，异常<0）。
	 * */
	int32 (*uart_get_status)(struct tag_UART_DEVICE *dev);

	bool block;		// 0:非阻塞；1:阻塞
	int32 uart_fd;
	int32 uart_index;
	char uart_dev_path[UART_DEV_PATH_SIZE];
} UART_DEVICE_T;

#ifdef __cplusplus
}
#endif

#endif
