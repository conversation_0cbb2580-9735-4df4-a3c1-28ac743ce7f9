
////////////////////////////////////////////////////////////////////////////////
#ifndef _CppSQLite3_H_
#define _CppSQLite3_H_

#include "sqlite3.h"
#include <cstdio>
#include <cstring>
#include <string>

#include "tzctypedef.h"

#ifdef WIN32		// windows platform
#  ifdef CPPSQLITE_DLL
#    define CPPSQLITE_API __declspec(dllexport)
#  else
#    define CPPSQLITE_API __declspec(dllimport)
#  endif
#  pragma warning( disable : 4251 4275) 
#else	// other platform
#  define CPPSQLITE_API
#endif // WIN32

using std::string;
#define CPPSQLITE_ERROR 1000

class CPPSQLITE_API CppSQLite3Exception
{
public:
	CppSQLite3Exception(const int nErrCode,
		const Jchar* szErrMess,
		bool bDeleteMsg=true);

	CppSQLite3Exception(const CppSQLite3Exception&  e);

	virtual ~CppSQLite3Exception();

	const int errorCode() { return mnErrCode; }

	const Jchar* errorMessage() { return mpszErrMess; }

	static const Jchar* errorCodeAsString(int nErrCode);

private:
	int mnErrCode;
	Jchar* mpszErrMess;
};


class CPPSQLITE_API CppSQLite3Buffer
{
public:
	CppSQLite3Buffer();

	~CppSQLite3Buffer();

	const Jchar* format(const Jchar* szFormat, ...);

	operator const Jchar*() { return mpBuf; }

	void clear();

private:
	Jchar* mpBuf;
};


class CPPSQLITE_API CppSQLite3Binary
{
public:
	CppSQLite3Binary();

	~CppSQLite3Binary();

	void setBinary(const Juint8* pBuf, int nLen);
	void setEncoded(const Juint8* pBuf);

	const Juint8* getEncoded();
	const Juint8* getBinary();

	int getBinaryLength();

	Juint8* allocBuffer(int nLen);

	void clear();

private:
	Juint8* mpBuf;
	int mnBinaryLen;
	int mnBufferLen;
	int mnEncodedLen;
	bool mbEncoded;
};

class CppSQLite3DB;

class CPPSQLITE_API CppSQLite3Query
{
public:
	CppSQLite3Query();

	CppSQLite3Query(const CppSQLite3Query& rQuery);

	CppSQLite3Query(sqlite3* pDB,
		sqlite3_stmt* pVM,
		bool bEof,
		bool bOwnVM=true);

	CppSQLite3Query& operator=(const CppSQLite3Query& rQuery);

	virtual ~CppSQLite3Query();

	bool IsOpen();
	void Edit();
	bool Update();

	bool Open(CppSQLite3DB * pdb, const Jchar * szTblName);

	int numFields();

	int fieldIndex(const Jchar* szField);
	const Jchar* fieldName(int nCol);

	const Jchar* fieldDeclType(int nCol);
	int fieldDataType(int nCol);

	const Jchar* fieldValue(int nField);
	const Jchar* fieldValue(const Jchar* szField);

	int getIntField(int nField, int nNullValue=0);
	int getIntField(const Jchar* szField, int nNullValue=0);

	Jdouble getFloatField(int nField, Jdouble fNullValue=0.0);
	Jdouble getFloatField(const Jchar* szField, Jdouble fNullValue=0.0);

	const Jchar* getStringField(int nField, const Jchar* szNullValue="");
	const Jchar* getStringField(const Jchar* szField, const Jchar* szNullValue="");

	const Juint8* getBlobField(int nField, int& nLen);
	const Juint8* getBlobField(const Jchar* szField, int& nLen);

	bool fieldIsNull(int nField);
	bool fieldIsNull(const Jchar* szField);

	bool eof();

	bool nextRow();

	void finalize();

	//====================================================================
	//修改记录的字段值
	//====================================================================
	string SetFieldValue(string & sFiled, Juint32 val);

	string SetFieldValue(string & sFiled, Jint32 val);

	string SetFieldValue(string & sFiled, string & val);

	string SetFieldValue(string & sFiled, CppSQLite3Binary & val);

	//====================================================================
	//获取记录的字段值
	//====================================================================
	bool GetFieldValue(string & sFiled, bool & val);

	bool GetFieldValue(string & sFiled, Juint8 & val);

	bool GetFieldValue(string & sFiled, Juint16 & val);

	bool GetFieldValue(string & sFiled, Juint32 & val);

	bool GetFieldValue(string & sFiled, Jint32 & val);

	bool GetFieldValue(string & sFiled, Jfloat & val);

	bool GetFieldValue(string & sFiled, Jdouble & val);

	bool GetFieldValue(string & sFiled, string & val);

	bool GetFieldValue(string & sFiled, CppSQLite3Binary & val);


private:
	void checkVM();

	sqlite3* mpDB;
	sqlite3_stmt* mpVM;
	bool mbEof;
	int mnCols;
	bool mbOwnVM;
};


class CPPSQLITE_API CppSQLite3Table
{
public:
	CppSQLite3Table();

	CppSQLite3Table(const CppSQLite3Table& rTable);

	CppSQLite3Table(Jchar** paszResults, int nRows, int nCols);

	virtual ~CppSQLite3Table();

	CppSQLite3Table& operator=(const CppSQLite3Table& rTable);

	int numFields();

	int numRows();

	const Jchar* fieldName(int nCol);

	const Jchar* fieldValue(int nField);
	const Jchar* fieldValue(const Jchar* szField);

	int getIntField(int nField, int nNullValue=0);
	int getIntField(const Jchar* szField, int nNullValue=0);

	Jdouble getFloatField(int nField, Jdouble fNullValue=0.0);
	Jdouble getFloatField(const Jchar* szField, Jdouble fNullValue=0.0);

	const Jchar* getStringField(int nField, const Jchar* szNullValue="");
	const Jchar* getStringField(const Jchar* szField, const Jchar* szNullValue="");

	bool fieldIsNull(int nField);
	bool fieldIsNull(const Jchar* szField);

	void setRow(int nRow);

	void finalize();

private:
	void checkResults();

	int mnCols;
	int mnRows;
	int mnCurrentRow;
	Jchar** mpaszResults;
};


class CPPSQLITE_API CppSQLite3Statement
{
public:

	CppSQLite3Statement();

	CppSQLite3Statement(const CppSQLite3Statement& rStatement);

	CppSQLite3Statement(sqlite3* pDB, sqlite3_stmt* pVM);

	virtual ~CppSQLite3Statement();

	CppSQLite3Statement& operator=(const CppSQLite3Statement& rStatement);

	int execDML();

	CppSQLite3Query execQuery();

	void bind(int nParam, const Jchar* szValue);
	void bind(int nParam, const int nValue);
	void bind(int nParam, const Jdouble dwValue);
	void bind(int nParam, const Juint8* blobValue, int nLen);
	void bindNull(int nParam);

	void reset();

	void finalize();

private:
	void checkDB();
	void checkVM();

	sqlite3* mpDB;
	sqlite3_stmt* mpVM;
};

class CPPSQLITE_API CppSQLite3DB
{
public:
	CppSQLite3DB();

	virtual ~CppSQLite3DB();

	bool IsOpen() const;

	bool open(const Jchar* szFile);

    bool open_readonly(const Jchar* szFile);

	void close();

	bool tableExists(const Jchar* szTable);

	int execDML(const Jchar* szSQL);

	CppSQLite3Query execQuery(const Jchar* szSQL);

	int execScalar(const Jchar* szSQL);

	CppSQLite3Table getTable(const Jchar* szSQL);

	CppSQLite3Statement compileStatement(const Jchar* szSQL);

	sqlite_int64 lastRowId();

	void interrupt() { sqlite3_interrupt(mpDB); }

	void setBusyTimeout(int nMillisecs);

	static const Jchar* SQLiteVersion() { return SQLITE_VERSION; }

private:
	CppSQLite3DB(const CppSQLite3DB& db);
	CppSQLite3DB& operator=(const CppSQLite3DB& db);

	sqlite3_stmt* compile(const Jchar* szSQL);

	void checkDB();

	sqlite3* mpDB;
	int mnBusyTimeoutMs;
};

#endif

