#!/bin/sh
PATH=$PATH:/usr/local/extapps/DTAnalyzer/bin
export PATH

APP_NAME=DTAnalyzer

kill_app(){

	PID=$(ps |grep $APP_NAME |grep -v grep|grep -v '.sh'|grep -v 'sqlite3'|grep -v 'log'|awk '{print $1}')
	aPID=$(($PID+0))
	if [ "0" != "$aPID" ]; then
		echo "kill $APP_NAME pid $aPID!"
		kill -9 $PID
		return
	fi
	
	PID=$(ps |grep $APP_NAME |grep -v grep|grep -v '.sh'|grep -v 'sqlite3'|grep -v 'log'|awk '{print $2}')
	aPID=$(($PID+0))
	if [ "0" != "$aPID" ]; then
		echo "kill $APP_NAME pid $aPID!"
		kill -9 $PID
		return
	fi
	
	PID=$(ps -ef |grep $APP_NAME |grep -v grep|grep -v '.sh'|grep -v 'log'|awk '{print $1}')
	aPID=$(($PID+0))
	if [ "0" != "$aPID" ]; then
		echo "kill $APP_NAME pid $aPID!"
		kill -9 $PID
		return
	fi
	
	PID=$(ps -ef |grep $APP_NAME |grep -v grep|grep -v '.sh'|grep -v 'log'|awk '{print $2}')
	aPID=$(($PID+0))
	if [ "0" != "$aPID" ]; then
		echo "kill $APP_NAME pid $aPID!"
		kill -9 $PID
		return
	fi
	
	echo "no $APP_NAME"
}

kill_app

dataShardpath=/data/app
mkdir -p $dataShardpath/DTAnalyzer
mkdir -p $dataShardpath/DTAnalyzer/logFile
mkdir -p $dataShardpath/DTAnalyzer/commFIle
mkdir -p $dataShardpath/DTAnalyzer/configFile

# 文件系统同步
sync;sync

chmod 755 /usr/local/extapps/DTAnalyzer/bin/DTAnalyzer

/usr/local/extapps/DTAnalyzer/bin/DTAnalyzer  >/dev/null  2>&1 &
echo "DTAnalyzer program started!"