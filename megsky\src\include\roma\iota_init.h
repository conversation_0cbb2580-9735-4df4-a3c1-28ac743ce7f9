/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012.
 */

#ifndef INC_AGENT_IOTA_INIT_H
#define INC_AGENT_IOTA_INIT_H

#include "hw_type.h"
#include <stdarg.h>


#ifdef __cplusplus
extern "C" {
#endif

/**
 * @param context the application-self defined parameter, current is NULL
 * @param messageId the inner messageId(1-65535) of the uplink message in this MQTT SDK
 * @param code the reason code for the failure callback
 * @param message the message can be from the IoT platform (e.g. command content) or from the MQTT SDK (e.g. failure explanation message)
 */
typedef HW_VOID (*PFN_CALLBACK_HANDLER)(HW_VOID* context, HW_INT messageId, HW_INT code, HW_CHAR *message);
typedef HW_VOID (*PFN_CALLBACK_HANDLER_TOPIC)(HW_VOID *context, HW_INT token, HW_INT code, const HW_CHAR *topic, HW_CHAR *message);
typedef HW_VOID (*PFN_LOG_CALLBACK_HANDLER)(int level, char* format, va_list args);

typedef enum enum_IOTA_CALLBACK_SETTING
{
    EN_IOTA_CALLBACK_CONNECT_SUCCESS     	 = 11,
    EN_IOTA_CALLBACK_CONNECT_FAILURE     	 = 12,
    EN_IOTA_CALLBACK_DISCONNECT_SUCCESS  	 = 13,
    EN_IOTA_CALLBACK_DISCONNECT_FAILURE  	 = 14,
    EN_IOTA_CALLBACK_CONNECTION_LOST     	 = 15,
											 
    EN_IOTA_CALLBACK_PUBLISH_SUCCESS     	 = 21,
    EN_IOTA_CALLBACK_PUBLISH_FAILURE     	 = 22,
											 
    EN_IOTA_CALLBACK_SUBSCRIBE_SUCCESS   	 = 31,
    EN_IOTA_CALLBACK_SUBSCRIBE_FAILURE   	 = 32,
											 
    EN_IOTA_CALLBACK_COMMAND_ARRIVED     	 = 41,

    EN_IOTA_CALLBACK_DEVICE_ADDITION_RESULT  = 51,
    EN_IOTA_CALLBACK_DEVICE_DELETION_RESULT  = 52,
    EN_IOTA_CALLBACK_DEVICE_UPDATE_RESULT    = 53,
    EN_IOTA_CALLBACK_DEVICE_QUERY_RESULT     = 54,

}EN_IOTA_CALLBACK_SETTING;


/**
* @brief	Initialize resources.
*
* @param [in] pcWorkPath : AgentLite working path(required)
* @param [in] pcLogPath  : Log path(Optional)
*
* @retval IOTA_SUCCESS 	                : Init success.
* @retval IOTA_RESOURCE_NOT_AVAILABLE	: Cannot use IOTA resources.
* @retval IOTA_PARAMETER_EMPTY       	: param is empty.
* @retval IOTA_INITIALIZATION_REPEATED	: Repeat initialization.
* @see iot_type.h.
*/
HW_API_FUNC HW_INT IOTA_Init(HW_CHAR *pcWorkPath, HW_CHAR *pcLogPath);

/**
* @brief	Destroy resources.
*
* @param [in] void
*
* @retval void.
*/
HW_API_FUNC HW_VOID IOTA_Destroy(void);

/**
* @brief	Set the callback function of Agent Lite related processes.
*
* @param [in] iItem               : Process points(required)  see EN_IOTA_CALLBACK_SETTING
* @param [in] pfnCallbackHandler  : Callback function(required)
*
* @retval void.
*/
HW_API_FUNC HW_VOID IOTA_SetCallback(HW_INT iItem, PFN_CALLBACK_HANDLER pfnCallbackHandler);

/**
* @brief	Set the callback function of Agent Lite Custom Topic.
*
* @param [in] iItem               : Process points(required)  see EN_IOTA_CALLBACK_SETTING
* @param [in] pfnCallbackHandler  : Callback function(required)
*
* @retval void.
*/
HW_API_FUNC void IOTA_SetCallbackWithTopic(PFN_CALLBACK_HANDLER_TOPIC pfnCallbackTopicHandler);


/**
* @brief	Set the log output function.
*
* @param [in] pfnLogCallbackHandler : Log output function(required)
*
* @retval void.
*/
HW_API_FUNC HW_VOID IOTA_SetPrintLogCallback(PFN_LOG_CALLBACK_HANDLER pfnLogCallbackHandler);

/**
* @brief	Set the Certificate file path.
*
* @param [in] pcCertPath : Certificate file path
*
* @retval  0  success.
* @retval -1  Failed.

*/
HW_API_FUNC HW_INT IOTA_SetCertPath(HW_CHAR *pcCertPath);



#ifdef __cplusplus
}
#endif
#endif

