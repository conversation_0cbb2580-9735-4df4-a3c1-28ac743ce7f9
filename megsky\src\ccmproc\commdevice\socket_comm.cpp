#include "socket_comm.h"
#include <signal.h>
//EMS-Mend: pengxiaoyan
//EMS-Mend<
//#ifdef HPUX
//#include "../../sb/sbmix/unix/include/sbplatform.h"
//#endif
//EMS-Mend>
// EMS-Mend< baizy 2007.6.2 14:38
// 修改原因:sbplatform.h中已经定义
//#ifdef AIX
//typedef socklen_t	SOCKLEN;
//#else
//typedef int	SOCKLEN;
//#endif 
typedef socklen_t SOCKLEN;
// EMS-Mend>


inline void TraceMessage(const char* /*chMessage*/)
{
}

CSocketComm::CSocketComm()
: m_socket(0)
, m_Type(Link_Unknown)
, m_ConnectState(SOCKET_NOTCONNECTTED)
{
}

CSocketComm::CSocketComm(s_socket_param sockaddr,bool ServerSocketFlag)
{
	SetSocketParam(sockaddr, ServerSocketFlag);
}

CSocketComm::CSocketComm(int sock,s_socket_param param)
{
	SetLinkSocket(sock, param);
}

CSocketComm::~CSocketComm()
{
	if (m_socket > 0)
	{
		Close();
	}
}

void CSocketComm::SetSocketParam(s_socket_param sockaddr, bool ServerSocketFlag)
{
	m_Type = sockaddr.type;		// 网络协议类型
	m_socket = 0;
	m_ConnectState = SOCKET_NOTCONNECTTED;
	memset(&m_clientparam,0,sizeof(m_clientparam));

	ConvertSockAddr(sockaddr, m_sockaddr,ServerSocketFlag);
	m_sockettype = ServerSocketFlag;
	m_isAlreadyListen = false;
}

void CSocketComm::SetLinkSocket(int sock,s_socket_param param)
{
	m_socket = sock;
	m_clientparam = param;
	m_ConnectState = SOCKET_CONNECTNORMAL;
}

// 断开连接
bool CSocketComm::Close()
{
	//==========================================================================
	//songxiaona 2015-04-23 add begin
	//问题：南戴河变电站，后台与华伟独立五防软件通信，奇数次连接时通信正常，偶数次连接时通信失败。
	//分析：socket关闭时该函数会被多次调用，如果调用close(0)函数后，
	//在之后调用的accept()函数很可能会返回0，导致连接建立失败
	if(0 == m_socket)
		return true;
	//songxiaona 2015-04-23 add end
	//==========================================================================

#if defined(WIN32)
	closesocket(m_socket) ;
#else
	close(m_socket);
#endif

	m_isAlreadyListen = false;
	m_socket = 0;
	m_ConnectState = SOCKET_NOTCONNECTTED;
	return true;
}

int CSocketComm::Connect()
{
	if (m_sockettype)// 服务socket
		return SOCKET_NOTCONNECTTED;

	if (m_ConnectState == SOCKET_CONNECTTING)			// 正在连接
	{
		InitRWSet(0, 1);// 初始化超时时间和fd_set
		int nResult = 0;
#if defined(WIN32)
		nResult = select(0, NULL, &m_writeSet, NULL, &m_tTimeout);
#else
		nResult = select(m_socket+1, NULL ,&m_writeSet, NULL, &m_tTimeout);
#endif	
		if (nResult <= 0)								// 连接没有建立
			Close();

		else if (nResult > 0)
		{
			if (FD_ISSET(m_socket, &m_writeSet))
			{
				m_ConnectState = SOCKET_CONNECTNORMAL;
				int error = 1;
				//服务端和客户端ip和Port不一致 add by songxiaona 2014-05-17
				bool bFlag(true);

#ifndef WIN32
				unsigned int len = sizeof(int);		// baizytmp, socklen_t
				getsockopt(m_socket, SOL_SOCKET, SO_ERROR, &error, &len);

				//add by songxiaona 2014-05-17 begin
				//本地(客户端)信息
				unsigned short usPort;
				char *usIp;
				struct sockaddr_in* addr;
				struct sockaddr tmpaddr;
				int tmpaddr_len = sizeof(tmpaddr);
				memset(&tmpaddr,0,tmpaddr_len);
				getsockname(m_socket,&tmpaddr,(socklen_t*)&tmpaddr_len);
				addr = (sockaddr_in*)&tmpaddr;
				usPort = ntohs(addr->sin_port);
				usIp = inet_ntoa(addr->sin_addr);

				//服务端信息
				unsigned short serPort = ntohs(m_sockaddr.sin_port);
				char *serIp;
				serIp = inet_ntoa(m_sockaddr.sin_addr);

				//比较端口号和端口号是否一致，若一致，则返回false
				if(strcmp(serIp, usIp)==0 && usPort == serPort)
				{
					bFlag = false;
				}
				//add by songxiaona 2014-05-17 end
#else
				int len = sizeof(int);
				getsockopt(m_socket, SOL_SOCKET, SO_ERROR, (char*)&error, &len);
#endif
				if (!error && bFlag)
					m_ConnectState = SOCKET_CONNECTNORMAL;
				else
					Close();
			}
			else
				Close();
		}
	}
	else if (m_ConnectState == SOCKET_NOTCONNECTTED)
	{
		if (InitSocket())							// socket初始化
		{
			int ret = connect(m_socket, (struct sockaddr*)&m_sockaddr, sizeof(m_sockaddr));
			if (ret < 0)
			{
#if defined(WIN32)
				if (WSAGetLastError() == WSAEWOULDBLOCK)// 非阻塞方式，连接不能立即完成
					m_ConnectState = SOCKET_CONNECTTING;
#else
				if (errno == EINPROGRESS || errno == 0)
					// 非阻塞方式，连接不能立即完成
					m_ConnectState = SOCKET_CONNECTTING;
#endif
				else// 连接失败
					Close();
			}
			else// 连接成功
				m_ConnectState = SOCKET_CONNECTNORMAL;
		}
	}

	return m_ConnectState;
}

// 接收数据
int CSocketComm::ReadData(unsigned char* data, int count)
{
	int readCount = 0;

	InitRWSet(0,1);
#if defined(WIN32)
	int nResult = select(0, &m_readSet, NULL, &m_exceptSet, &m_tTimeout);
#else
	int nResult = select(m_socket+1, &m_readSet, NULL, &m_exceptSet, &m_tTimeout);
#endif
	if (nResult < 0)
	{
		readCount = -1;
	}
	else if (nResult > 0)
	{
		if (FD_ISSET(m_socket, &m_exceptSet))
		{
			Close();
			return -1;
		}
		if (FD_ISSET(m_socket, &m_readSet))
		{
			readCount = recv(m_socket, (char*)data, count, 0);
			if (readCount <= 0)
			{
				// 有错误 EWOULDBLOCK####
#if defined(WIN32)
				int nwsaErr = WSAGetLastError();
				if ( nwsaErr == WSAEWOULDBLOCK)
				{
					readCount = 0;
				}
#else
				if (errno == ENOBUFS || errno == EAGAIN || errno == EWOULDBLOCK) 
				{
					readCount = 0;
				}
#endif
				else
				{
					readCount = -1;
				}
			}
		}
		else
		{
			readCount = -1;
		}
	} 
	else
	{
		if (FD_ISSET(m_socket, &m_exceptSet))
		{
			readCount = -1;
		}
	}
	if (readCount == -1)
		Close();
	return readCount;
}

// 发送数据
int CSocketComm::WriteData(unsigned char* data, int count)
{
	int writeCount = -1;

	InitRWSet(0,1);
#if defined(WIN32)
	int nResult = select(0, NULL, &m_writeSet, &m_exceptSet, &m_tTimeout);
#else
	int nResult = select(m_socket+1, NULL, &m_writeSet, &m_exceptSet, &m_tTimeout);
#endif
	if (nResult < 0)
	{
		// 出错处理
		char tError[32];
		sprintf(tError, "socket 通讯select错误, 错误号：%d!!!!", errno);
		TraceMessage(tError);
		writeCount = -1;
	}
	else if (nResult > 0)
	{
		if (FD_ISSET(m_socket, &m_exceptSet))
		{
			Close();
			return -1;
		}
		if(FD_ISSET(m_socket,&m_writeSet))
		{
			writeCount = send(m_socket, (char*)data, count, 0);
			if (writeCount < 0)
			{
				// 有错误
#if defined(WIN32)
				if (WSAGetLastError() == WSAEWOULDBLOCK)
				{
					// 等待
					writeCount = 0;
				}
#else
				if (errno == EWOULDBLOCK)
				{
					writeCount = 0;
					TraceMessage("发送数据出错!!!!");
				}
#endif
				else
				{
					writeCount = -1;
					TraceMessage("socket 通讯send错误!!!!");
				}
			}
		}
		else
			writeCount = -1;
	}
	else
	{
		if (FD_ISSET(m_socket, &m_exceptSet))
		{
			writeCount = -1;
		}
	}

	if (writeCount == -1)
		Close();
	return writeCount;
}

// 设定监听多少个客户端，并开始监听
bool CSocketComm::Listen(short clientCount)
{
	int ret = 0;
	//socket初始化成功
	if (!m_sockettype)
	{
		//如果是客户socket，则不能进行连接操作
		return false;
	}
	if (m_socket)
		return true;
	if (InitSocket())
	{
		ret = bind(m_socket, (sockaddr*)&m_sockaddr, sizeof(sockaddr));
		if (ret >= 0)
		{
			ret = listen(m_socket, clientCount);
		}
		if (ret < 0)
		{
			Close();
			return false;
		}
	}
	else
	{
		return false;
	}
	m_isAlreadyListen = true;

	//增加重用IP地址
	int nReuseAddr = 1;
	int nRet = setsockopt (m_socket, SOL_SOCKET, SO_REUSEADDR, (char*)&nReuseAddr, sizeof(nReuseAddr));

	return true;
}

// 创建一个对象和客户端连接，只用于TCP服务器
CSocketComm* CSocketComm::Accept()
{
	int clientSocket  = 0;
	sockaddr_in client_addr;
	
	SOCKLEN length = sizeof(client_addr);
	memset(&client_addr,0,sizeof(client_addr));
#ifdef HPUX //pengxiaoyan modify for socket
//	fd_set readSet;
//	fd_set writeSet;
//	FD_ZERO(&m_readSet);
//  FD_SET(m_socket,&m_readSet);
	InitRWSet(0,1);	//xuwb add
//	if (select(m_socket+1,&m_readSet,NULL,NULL,NULL) > 0)
//		clientSocket = accept(m_socket, (sockaddr*)&client_addr, &length);
	if (select(m_socket+1,&m_readSet, NULL, &m_exceptSet, &m_tTimeout) > 0)
	{
		if (FD_ISSET(m_socket, &m_exceptSet))
		{
			Close();
			return NULL;
		}
		if (FD_ISSET(m_socket, &m_readSet))
			clientSocket = accept(m_socket, (sockaddr*)&client_addr, &length);
	}	
#else	
#	ifdef SUN
	clientSocket = accept(m_socket, (sockaddr*)&client_addr, (size_t*)&length);
#	else
	clientSocket = accept(m_socket, (sockaddr*)&client_addr, &length);
#	endif
#endif//end HPUX
	if (clientSocket  < 0)
	{
#if defined(WIN32)
		int nError = WSAGetLastError();
		if (WSAGetLastError() != WSAEWOULDBLOCK)
		{
			Close();
		}
#else
		if (errno != EWOULDBLOCK && errno != 0)		
		{
			Close();
		}
#endif
		return NULL;
	}
	else if(clientSocket  == 0)//xuwb add
		return NULL;

	s_socket_param clientparam;
	memset(&clientparam,0,sizeof(clientparam));
	clientparam.type = TCP_TYPE;
	strcpy(clientparam.IPAddress , inet_ntoa(client_addr.sin_addr));
	clientparam.portNumber = ntohs(client_addr.sin_port);
	return new CSocketComm(clientSocket,clientparam);
}

bool CSocketComm::Accept(s_link_sock & link)
{
	int clientSocket  = 0;
	sockaddr_in client_addr;
	
	SOCKLEN length = sizeof(client_addr);
	memset(&client_addr,0,sizeof(client_addr));
#ifdef HPUX //pengxiaoyan modify for socket
//	fd_set readSet;
//	fd_set writeSet;
//	FD_ZERO(&m_readSet);
//  FD_SET(m_socket,&m_readSet);
	InitRWSet(0,1);	//xuwb add
//	if (select(m_socket+1,&m_readSet,NULL,NULL,NULL) > 0)
//		clientSocket = accept(m_socket, (sockaddr*)&client_addr, &length);
	if (select(m_socket+1,&m_readSet, NULL, &m_exceptSet, &m_tTimeout) > 0)
	{
		if (FD_ISSET(m_socket, &m_exceptSet))
		{
			Close();
			return NULL;
		}
		if (FD_ISSET(m_socket, &m_readSet))
			clientSocket = accept(m_socket, (sockaddr*)&client_addr, &length);
	}	
#else	
#	ifdef SUN
	clientSocket = accept(m_socket, (sockaddr*)&client_addr, (size_t*)&length);
#	else
	clientSocket = accept(m_socket, (sockaddr*)&client_addr, &length);
#	endif
#endif//end HPUX
	if (clientSocket  < 0)
	{
#if defined(WIN32)
		int nError = WSAGetLastError();
		if (WSAGetLastError() != WSAEWOULDBLOCK)
		{
			Close();
		}
#else
		if (errno != EWOULDBLOCK && errno != 0)		
		{
			Close();
		}
#endif
		return false;
	}
	else if(clientSocket  == 0)//xuwb add
		return false;

	s_socket_param clientparam;
	memset(&clientparam,0,sizeof(clientparam));
	clientparam.type = TCP_TYPE;
	strcpy(clientparam.IPAddress , inet_ntoa(client_addr.sin_addr));
	clientparam.portNumber = ntohs(client_addr.sin_port);

	link.s = clientSocket;
	link.param = clientparam;
	return true;
}

void CSocketComm::CloseSocket(int sock_id)
{
#if defined(WIN32)
	::shutdown(sock_id, SD_BOTH);
	::closesocket(sock_id);
#else
	close(sock_id);
#endif
}

// 将Socket地址结构转到标准结构
void CSocketComm::ConvertSockAddr(s_socket_param addr1, sockaddr_in& addr2,bool ListenFlag)
{
	memset(&addr2, 0, sizeof(addr2));
	addr2.sin_family = AF_INET;
	addr2.sin_port = htons(addr1.portNumber);
	
	// 2015-02-06 By Zhaobo ListenFlag bind函数中的ip参数置0
	// 由内核自己选择分配IP 导致多个网口配置104规约时只能有一个网口可以连接
	//if (ListenFlag)
	//	addr2.sin_addr.s_addr=htonl(INADDR_ANY); 
	//else
	addr2.sin_addr.s_addr = inet_addr(addr1.IPAddress); 
}

// 读写设备初始化
void CSocketComm::InitRWSet(long nSec,long uSec )
{
	FD_ZERO(&m_readSet);
	FD_SET(m_socket,&m_readSet);
	FD_ZERO(&m_writeSet);
	FD_SET(m_socket,&m_writeSet);
	FD_ZERO(&m_exceptSet);
	FD_SET(m_socket,&m_exceptSet);
	
	m_tTimeout.tv_sec=nSec;//读线程中的定时等待
	m_tTimeout.tv_usec=uSec;
}

int CSocketComm::InitSocket()
{
	if (!m_socket)
	{
		if (m_Type == UDP_TYPE)
		{
			m_socket = socket(AF_INET, SOCK_DGRAM,0);
		}
		else // if (m_Type == TCP_TYPE)
		{
			m_socket = socket(AF_INET,SOCK_STREAM,0);
			if (0 == m_socket)// 测试有等于零的情况，重新创建一次；问题现象是调度主站在切机重新连接异常问题；2016-5-11 liujingke && guozf
				m_socket = socket(AF_INET,SOCK_STREAM,0);
		}
		
#if defined(WIN32)
		if (m_socket != INVALID_SOCKET)
		{
			// 设置为非阻塞方式连接
			unsigned long ul = 1;
			if (ioctlsocket(m_socket, FIONBIO, &ul) == SOCKET_ERROR)
			{
				Close();
			}
			// 复用IP
			if (m_sockettype)
			{
				BOOL bBroadcast = TRUE;
				if (setsockopt (m_socket, SOL_SOCKET, SO_REUSEADDR, (char*)&bBroadcast, sizeof(bBroadcast)) != 0)
					Close();
			}
/*			struct timeval	timer;
			timer.tv_sec  = 10;
			timer.tv_usec = 0;
			setsockopt(m_socket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timer, sizeof(timer));
			setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timer, sizeof(timer));
*/
		}
#else
		if (m_socket > 0)
		{
			// unix socket 设置非阻塞方式
			//int flags = fcntl(m_socket, F_GETFL, 0);
			if (fcntl(m_socket, F_SETFL, O_NONBLOCK) < 0)
			{
				Close();
			}
			// 复用IP
			if (m_sockettype)
			{
				const int bBroadcast = 1;
				if (setsockopt (m_socket, SOL_SOCKET, SO_REUSEADDR, (char*)&bBroadcast, sizeof(bBroadcast)) != 0)
					Close();
			}
/*
			struct timeval	timer;
			timer.tv_sec  = 5;
			timer.tv_usec = 0;
			setsockopt(m_socket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timer, sizeof(timer));
			setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timer, sizeof(timer));
*/
			// 把错误码设为0，为了在后面的连接判断错误码的时候，不会被以前的错误码影响
			errno = 0;
		}
#endif
		else
		{
			// 正常情况下m_socket无效时应该是-1 ,此处赋值0有待研究； add 2016-5-12 liujingke && guozf
			m_socket = 0;
		}
	}
	return m_socket;
}

s_socket_param CSocketComm::GetClientParam()
{
	return m_clientparam;
}

int CSocketComm::GetSocket()
{
	return m_socket;
}

CIIString CSocketComm::getIP()
{
	char ip[32];
	sprintf(ip, "%s:%d", m_clientparam.IPAddress, m_clientparam.portNumber);
	return CIIString((const char*)ip);
}

bool CSocketComm::isAlreadyListen()
{
	return m_isAlreadyListen;
}
