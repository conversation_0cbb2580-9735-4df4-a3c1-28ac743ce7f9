#ifndef _BASIC_DEF_
#define _BASIC_DEF_
#include <arpa/inet.h>
#define vt 0x20000200                          // 电压
#define active_power_total 0x00000200          // 组合有功电能
#define active_power_forward 0x00100200        // 正向有功电能
#define active_power_forward_a 0x00110200      // A 相正向有功电能
#define active_power_forward_b 0x00120200      // B 相正向有功电能
#define active_power_forward_c 0x00130200      // C 相正向有功电能
#define active_power_reverse 0x00200200        // 反向有功电能
#define active_power_reverse_a 0x00210200      // A 相反向有功电能
#define active_power_reverse_b 0x00220200      // B 相反向有功电能
#define active_power_reverse_c 0x00230200      // C 相反向有功电能
#define reactive_power_1_total 0x00300200      // 组合无功 1 电能
#define reactive_power_1_a 0x00310200          // A 相组合无功 1 电能
#define reactive_power_1_b 0x00320200          // B 相组合无功 1 电能
#define reactive_power_1_c 0x00330200          // C 相组合无功 1 电能
#define reactive_power_2_total 0x00400200      // 组合无功 2 电能
#define reactive_power_2_a 0x00410200          // A 相组合无功 2 电能
#define reactive_power_2_b 0x00420200          // B 相组合无功 2 电能
#define reactive_power_2_c 0x00430200          // C 相组合无功 2 电能
#define reactive_power_quadrant_1 0x00500200   // 第一象限无功电能
#define reactive_power_quadrant_1_a 0x00510200 // A 相第一象限无功电能
#define reactive_power_quadrant_1_b 0x00520200 // B 相第一象限无功电能
#define reactive_power_quadrant_1_c 0x00530200 // C 相第一象限无功电能
#define reactive_power_quadrant_2 0x00600200   // 第二象限无功电能
#define reactive_power_quadrant_2_a 0x00610200 // A 相第二象限无功电能
#define reactive_power_quadrant_2_b 0x00620200 // B 相第二象限无功电能
#define reactive_power_quadrant_2_c 0x00630200 // C 相第二象限无功电能
#define reactive_power_quadrant_3 0x00700200   // 第三象限无功电能
#define reactive_power_quadrant_3_a 0x00710200 // A 相第三象限无功电能
#define reactive_power_quadrant_3_b 0x00720200 // B 相第三象限无功电能
#define reactive_power_quadrant_3_c 0x00730200 // C 相第三象限无功电能
#define reactive_power_quadrant_4 0x00800200   // 第四象限无功电能
#define reactive_power_quadrant_4_a 0x00810200 // A 相第四象限无功电能
#define reactive_power_quadrant_4_b 0x00820200 // B 相第四象限无功电能
#define reactive_power_quadrant_4_c 0x00830200 // C 相第四象限无功电能
#define apparent_power_forward 0x00900200      // 正向视在电能
#define apparent_power_forward_a 0x00910200    // A 相正向视在电能
#define apparent_power_forward_b 0x00920200    // B 相正向视在电能
#define apparent_power_forward_c 0x00930200    // C 相正向视在电能
#define apparent_power_reverse 0x00A00200      // 反向视在电能
#define apparent_power_reverse_a 0x00A10200    // A 相反向视在电能
#define apparent_power_reverse_b 0x00A20200    // B 相反向视在电能
#define apparent_power_reverse_c 0x00A30200    // C 相反向视在电能


#define active_power_max_demand_forward 0x10100200        // 正向有功最大需量
#define active_power_max_demand_forward_a 0x10110200      // A 相正向有功最大需量
#define active_power_max_demand_forward_b 0x10120200      // B 相正向有功最大需量
#define active_power_max_demand_forward_c 0x10130200      // C 相正向有功最大需量
#define active_power_max_demand_reverse 0x10200200        // 反向有功最大需量
#define active_power_max_demand_reverse_a 0x10210200      // A 相反向有功最大需量
#define active_power_max_demand_reverse_b 0x10220200      // B 相反向有功最大需量
#define active_power_max_demand_reverse_c 0x10230200      // C 相反向有功最大需量
#define reactive_power_1_max_demand_total 0x10300200      // 组合无功 1 最大需量
#define reactive_power_1_max_demand_a 0x10310200          // A 相组合无功 1 最大需量
#define reactive_power_1_max_demand_b 0x10320200          // B 相组合无功 1 最大需量
#define reactive_power_1_max_demand_c 0x10330200          // C 相组合无功 1 最大需量
#define reactive_power_2_max_demand_total 0x10400200      // 组合无功 2 最大需量
#define reactive_power_2_max_demand_a 0x10410200          // A 相组合无功 2 最大需量
#define reactive_power_2_max_demand_b 0x10420200          // B 相组合无功 2 最大需量
#define reactive_power_2_max_demand_c 0x10430200          // C 相组合无功 2 最大需量
#define reactive_power_quadrant_1_max_demand 0x10500200   // 第一象限最大需量
#define reactive_power_quadrant_1_max_demand_a 0x10510200 // A 相第一象限最大需量
#define reactive_power_quadrant_1_max_demand_b 0x10520200 // B 相第一象限最大需量
#define reactive_power_quadrant_1_max_demand_c 0x10530200 // C 相第一象限最大需量
#define reactive_power_quadrant_2_max_demand 0x10600200   // 第二象限最大需量
#define reactive_power_quadrant_2_max_demand_a 0x10610200 // A 相第二象限最大需量
#define reactive_power_quadrant_2_max_demand_b 0x10620200 // B 相第二象限最大需量
#define reactive_power_quadrant_2_max_demand_c 0x10630200 // C 相第二象限最大需量
#define reactive_power_quadrant_3_max_demand 0x10700200   // 第三象限最大需量
#define reactive_power_quadrant_3_max_demand_a 0x10710200 // A 相第三象限最大需量
#define reactive_power_quadrant_3_max_demand_b 0x10720200 // B 相第三象限最大需量
#define reactive_power_quadrant_3_max_demand_c 0x10730200 // C 相第三象限最大需量
#define reactive_power_quadrant_4_max_demand 0x10800200   // 第四象限最大需量
#define reactive_power_quadrant_4_max_demand_a 0x10810200 // A 相第四象限最大需量
#define reactive_power_quadrant_4_max_demand_b 0x10820200 // B 相第四象限最大需量
#define reactive_power_quadrant_4_max_demand_c 0x10830200 // C 相第四象限最大需量
#define apparent_power_max_demand_forward 0x10900200      // 正向视在最大需量
#define apparent_power_max_demand_forward_a 0x10910200    // A 相正向视在最大需量
#define apparent_power_max_demand_forward_b 0x10920200    // B 相正向视在最大需量
#define apparent_power_max_demand_forward_c 0x10930200    // C 相正向视在最大需量
#define apparent_power_max_demand_reverse 0x10A00200      // 反向视在最大需量
#define apparent_power_max_demand_reverse_a 0x10A10200    // A 相反向视在最大需量
#define apparent_power_max_demand_reverse_b 0x10A20200    // B 相反向视在最大需量
#define apparent_power_max_demand_reverse_c 0x10A30200    // C 相反向视在最大需量


// #define TOTAL_ACTIVE_ENERGY
// #define FORWARD_ACTIVE_ENERGY
// #define A_PHASE_FORWARD_ACTIVE_ENERGY
// #define B_PHASE_FORWARD_ACTIVE_ENERGY
// #define C_PHASE_FORWARD_ACTIVE_ENERGY
// #define REVERSE_ACTIVE_ENERGY
// #define A_PHASE_REVERSE_ACTIVE_ENERGY
// #define B_PHASE_REVERSE_ACTIVE_ENERGY
// #define C_PHASE_REVERSE_ACTIVE_ENERGY
// #define TOTAL_REACTIVE_1_ENERGY
// #define A_PHASE_TOTAL_REACTIVE_1_ENERGY
// #define B_PHASE_TOTAL_REACTIVE_1_ENERGY
// #define C_PHASE_TOTAL_REACTIVE_1_ENERGY
// #define TOTAL_REACTIVE_2_ENERGY
// #define A_PHASE_TOTAL_REACTIVE_2_ENERGY
// #define B_PHASE_TOTAL_REACTIVE_2_ENERGY
// #define C_PHASE_TOTAL_REACTIVE_2_ENERGY
// #define QUADRANT_1_REACTIVE_ENERGY
// #define A_PHASE_QUADRANT_1_REACTIVE_ENERGY
// #define B_PHASE_QUADRANT_1_REACTIVE_ENERGY
// #define C_PHASE_QUADRANT_1_REACTIVE_ENERGY
// #define QUADRANT_2_REACTIVE_ENERGY
// #define A_PHASE_QUADRANT_2_REACTIVE_ENERGY
// #define B_PHASE_QUADRANT_2_REACTIVE_ENERGY
// #define C_PHASE_QUADRANT_2_REACTIVE_ENERGY
// #define QUADRANT_3_REACTIVE_ENERGY
// #define A_PHASE_QUADRANT_3_REACTIVE_ENERGY
// #define B_PHASE_QUADRANT_3_REACTIVE_ENERGY
// #define C_PHASE_QUADRANT_3_REACTIVE_ENERGY
// #define QUADRANT_4_REACTIVE_ENERGY
// #define A_PHASE_QUADRANT_4_REACTIVE_ENERGY
// #define B_PHASE_QUADRANT_4_REACTIVE_ENERGY
// #define C_PHASE_QUADRANT_4_REACTIVE_ENERGY
// #define FORWARD_APPARENT_ENERGY
// #define A_PHASE_FORWARD_APPARENT_ENERGY
// #define B_PHASE_FORWARD_APPARENT_ENERGY
// #define C_PHASE_FORWARD_APPARENT_ENERGY
// #define REVERSE_APPARENT_ENERGY
// #define A_PHASE_REVERSE_APPARENT_ENERGY
// #define B_PHASE_REVERSE_APPARENT_ENERGY
// #define C_PHASE_REVERSE_APPARENT_ENERGY

#define voltage 0x20000200                     // 电压
#define voltage_zero_sequence 0x20000400       // 零序电压
#define current 0x20010200                     // 电流
#define current_zero_sequence 0x20010600       // 零序电流
#define voltage_phase_angle 0x20020200         // 电压相角
#define voltage_current_phase_angle 0x20030200 // 电压电流相角
#define active_power 0x20040200                // 有功功率
#define reactive_power 0x20050200              // 无功功率
#define apparent_power 0x20060200              // 视在功率
#define power_factor 0x200A0200                // 功率因数
#define grid_frequency 0x200F0200              // 电网频率
#define meter_status_word 0x20140200           // 电能表运行状态字
#define voltage_unbalance_rate 0x20260200      // 电压不平衡率
#define current_unbalance_rate 0x20270200      // 电流不平衡率



// #define VOLTAGE
// #define ZERO_SEQUENCE_VOLTAGE
// #define CURRENT
// #define ZERO_SEQUENCE_CURRENT
// #define VOLTAGE_PHASE_ANGLE
// #define VOLTAGE_CURRENT_PHASE_ANGLE
// #define ACTIVE_POWER
// #define REACTIVE_POWER
// #define APPARENT_POWER
// #define POWER_FACTOR
// #define GRID_FREQUENCY
// #define METER_RUNNING_STATUS_WORD
// #define VOLTAGE_UNBALANCE_RATE
// #define CURRENT_UNBALANCE_RATE


#define voltage_harmonic_rate_a 0x200D0200    // A 相电压谐波含有率（总及 2…n 次）
#define voltage_harmonic_rate_b 0x200D0300    // B 相电压谐波含有率（总及 2…n 次）
#define voltage_harmonic_rate_c 0x200D0400    // C 相电压谐波含有率（总及 2…n 次）
#define current_harmonic_rate_a 0x200E0200    // A 相电流谐波含有率（总及 2…n 次）
#define current_harmonic_rate_b 0x200E0300    // B 相电流谐波含有率（总及 2…n 次）
#define current_harmonic_rate_c 0x200E0400    // C 相电流谐波含有率（总及 2…n 次）
#define voltage_harmonic_content_a 0x20330200 // A 相电压谐波含量    （2…n 次）
#define voltage_harmonic_content_b 0x20330300 // B 相电压谐波含量    （2…n 次）
#define voltage_harmonic_content_c 0x20330400 // C 相电压谐波含量    （2…n 次）
#define current_harmonic_content_a 0x20340200 // A 相电流谐波含量    （2…n 次）
#define current_harmonic_content_b 0x20340300 // B 相电流谐波含量    （2…n 次）
#define current_harmonic_content_c 0x20340400 // C 相电流谐波含量    （2…n 次）


// #define A_PHASE_VOLTAGE_HARMONIC_RATE
// #define B_PHASE_VOLTAGE_HARMONIC_RATE
// #define C_PHASE_VOLTAGE_HARMONIC_RATE
// #define A_PHASE_CURRENT_HARMONIC_RATE
// #define B_PHASE_CURRENT_HARMONIC_RATE
// #define C_PHASE_CURRENT_HARMONIC_RATE
// #define A_PHASE_VOLTAGE_HARMONIC_CONTENT
// #define B_PHASE_VOLTAGE_HARMONIC_CONTENT
// #define C_PHASE_VOLTAGE_HARMONIC_CONTENT
// #define A_PHASE_CURRENT_HARMONIC_CONTENT
// #define B_PHASE_CURRENT_HARMONIC_CONTENT
// #define C_PHASE_CURRENT_HARMONIC_CONTENT


#define Generic_messages 0x0000             // 通用消息
#define G_m_Transparent_transmission 0x0012 // 透明传输
#define G_m_Version_information 0x0012      // 版本信息

// #define dataCenter 0x0005                    // 数据中心
#define dataCenter_dataup 0x0001             // 数据更新事件
// #define dataCenter_read_data 0x0010          // 读普通数据
// #define dataCenter_write_data 0x0011         // 写普通数据
#define dataCenter_reading_lg_data 0x0012    // 开始读记录型数据
#define dataCenter_get_real_time_data 0x0081 // 获取实时数据

#define secManager 0x0006 // 安全管理接口

#define IID_DATACENTER                          0x0005      //数据中心
#define IOP_DATACENTER_DATA_UPDATE_EVENT        0x0001      //数据更新事件
#define IOP_DATACENTER_INIT_EVENT               0x0002      //初始化事件
#define IOP_DATACENTER_READ_COMMON_DATA         0x0010      //读取普通数据
#define IOP_DATACENTER_WRITE_COMMON_DATA        0x0011      //写取普通数据
#define IOP_DATACENTER_READ_RECORD_DATA_START   0x0012      //开始读记录型数据
#define IOP_DATACENTER_READ_RECORD_DATA_STEP    0x0013      //单步读取记录型数据
#define IOP_DATACENTER_READ_RECORD_DATA_STOP    0x0014      //结束读取记录型数据
#define IOP_DATACENTER_WRITE_RECORD_DATA_START  0x0015      //开始写记录型数据
#define IOP_DATACENTER_UPDATE_RECORD_DATA       0x0016      //更新记录型数据
#define IOP_DATACENTER_GET_RECORD_COUNT         0x0020      //获取记录型数据条数
#define IOP_DATACENTER_GET_RECORD_DEEP          0x0021      //获取记录型数据深度
#define IOP_DATACENTER_DATA_INIT                0x0030      //数据初始化
#define IOP_DATACENTER_CLEAR_DATA_INDEX         0x0031      //清楚指定数据
#define IOP_DATACENTER_RESET_DATA               0x0032      //恢复出厂设置

#define IOP_DATACENTER_GET_DEV_REGISTER_INFO    0x0081      //获取设备注册列表
#define IOP_DATACENTER_GET_REALTIME_DATA        0x0082      //获取实时数据
#define IOP_DATACENTER_READ_FROZEN_DATA_START   0x0083      //开始读冻结数据
#define IOP_DATACENTER_READ_FROZEN_DATA_STEP    0x0084      //单步读冻结数据
#define IOP_DATACENTER_READ_FROZEN_DATA_STOP    0x0085      //结束读冻结数据
#define IOP_DATACENTER_READ_EVENT_DATA          0x0086      //事件数据读取

typedef enum
{
    TYPE_null = 0,
    TYPE_array,
    TYPE_structure,
    TYPE_bool,
    TYPE_bit_string,
    TYPE_double_long,
    TYPE_double_long_unsigned,
    TYPE_octet_string = 9,
    TYPE_visible_string,
    TYPE_UTF8_string = 12,
    TYPE_integer = 15,
    TYPE_long_signed,
    TYPE_unsigned_char,
    TYPE_long_unsigned,
    TYPE_long64 = 20,
    TYPE_long64_unsigned,
    TYPE_enum,
    TYPE_float32,
    TYPE_float64,
    TYPE_date_time,
    TYPE_date,
    TYPE_time,
    TYPE_date_time_s,
    TYPE_sequence = 48,
    TYPE_OI = 80,
    TYPE_OAD,
    TYPE_ROAD,
    TYPE_OMD,
    TYPE_TI,
    TYPE_TSA,
    TYPE_MAC,
    TYPE_RN,
    TYPE_Region,
    TYPE_Scaler_Unit,
    TYPE_RSD,
    TYPE_CSD,
    TYPE_MS,
    TYPE_SID,
    TYPE_SID_MAC,
    TYPE_COMDCB,
    TYPE_RCSD
} DATA;

typedef enum
{
    rspSample = 0, // 遥信脉冲采样,电表采集任务管理,低压集抄
    acMeter,       // 交采计量
    lcMonitor,     // 回路状态监测
    pqAnalyzer,    // 电能质量分析
    ocAmr,         // 电动汽车有序充电
    demAmr,        // 分布式能源管理
    eeemAmr        // 企业能效管理
} Log_dev_number;

// typedef enum
// {
//     IID=0,

// } IID;

// typedef enum
// {
//     IID=0,

// } IOP;

typedef struct MSG_TAG
{
    short IID;
    short IOP;
} msg_tag;

// typedef struct DIINFO
// {
//     unsigned int data_no;  //数据项标识
//     unsigned char attributeid; //属性标识
// };

// typedef struct basic_data
// {
//     DIINFO DATA_info; //数据项标识
//     unsigned short dataclassid;//数据类标识
//     unsigned databuffer;//数据缓冲
// };

// double active_power_total;          // 组合有功电能
// double active_power_forward;        // 正向有功电能
// double active_power_forward_a;      // A 相正向有功电能
// double active_power_forward_b;      // B 相正向有功电能
// double active_power_forward_c;      // C 相正向有功电能
// double active_power_reverse;        // 反向有功电能
// double active_power_reverse_a;      // A 相反向有功电能
// double active_power_reverse_b;      // B 相反向有功电能
// double active_power_reverse_c;      // C 相反向有功电能
// double reactive_power_1_total;      // 组合无功 1 电能
// double reactive_power_1_a;          // A 相组合无功 1 电能
// double reactive_power_1_b;          // B 相组合无功 1 电能
// double reactive_power_1_c;          // C 相组合无功 1 电能
// double reactive_power_2_total;      // 组合无功 2 电能
// double reactive_power_2_a;          // A 相组合无功 2 电能
// double reactive_power_2_b;          // B 相组合无功 2 电能
// double reactive_power_2_c;          // C 相组合无功 2 电能
// double reactive_power_quadrant_1;   // 第一象限无功电能
// double reactive_power_quadrant_1_a; // A 相第一象限无功电能
// double reactive_power_quadrant_1_b; // B 相第一象限无功电能
// double reactive_power_quadrant_1_c; // C 相第一象限无功电能
// double reactive_power_quadrant_2;   // 第二象限无功电能
// double reactive_power_quadrant_2_a; // A 相第二象限无功电能
// double reactive_power_quadrant_2_b; // B 相第二象限无功电能
// double reactive_power_quadrant_2_c; // C 相第二象限无功电能
// double reactive_power_quadrant_3;   // 第三象限无功电能
// double reactive_power_quadrant_3_a; // A 相第三象限无功电能
// double reactive_power_quadrant_3_b; // B 相第三象限无功电能
// double reactive_power_quadrant_3_c; // C 相第三象限无功电能
// double reactive_power_quadrant_4;   // 第四象限无功电能
// double reactive_power_quadrant_4_a; // A 相第四象限无功电能
// double reactive_power_quadrant_4_b; // B 相第四象限无功电能
// double reactive_power_quadrant_4_c; // C 相第四象限无功电能
// double apparent_power_forward;      // 正向视在电能
// double apparent_power_forward_a;    // A 相正向视在电能
// double apparent_power_forward_b;    // B 相正向视在电能
// double apparent_power_forward_c;    // C 相正向视在电能
// double apparent_power_reverse;      // 反向视在电能
// double apparent_power_reverse_a;    // A 相反向视在电能
// double apparent_power_reverse_b;    // B 相反向视在电能
// double apparent_power_reverse_c;    // C 相反向视在电能

// double active_power_max_demand_forward;        // 正向有功最大需量
// double active_power_max_demand_forward_a;      // A 相正向有功最大需量
// double active_power_max_demand_forward_b;      // B 相正向有功最大需量
// double active_power_max_demand_forward_c;      // C 相正向有功最大需量
// double active_power_max_demand_reverse;        // 反向有功最大需量
// double active_power_max_demand_reverse_a;      // A 相反向有功最大需量
// double active_power_max_demand_reverse_b;      // B 相反向有功最大需量
// double active_power_max_demand_reverse_c;      // C 相反向有功最大需量
// double reactive_power_1_max_demand_total;      // 组合无功 1 最大需量
// double reactive_power_1_max_demand_a;          // A 相组合无功 1 最大需
// double reactive_power_1_max_demand_b;          // B 相组合无功 1 最大需
// double reactive_power_1_max_demand_c;          // C 相组合无功 1 最大需
// double reactive_power_2_max_demand_total;      // 组合无功 2 最大需量
// double reactive_power_2_max_demand_a;          // A 相组合无功 2 最大需
// double reactive_power_2_max_demand_b;          // B 相组合无功 2 最大需
// double reactive_power_2_max_demand_c;          // C 相组合无功 2 最大需
// double reactive_power_quadrant_1_max_demand;   // 第一象限最大需量
// double reactive_power_quadrant_1_max_demand_a; // A 相第一象限最大需量
// double reactive_power_quadrant_1_max_demand_b; // B 相第一象限最大需量
// double reactive_power_quadrant_1_max_demand_c; // C 相第一象限最大需量
// double reactive_power_quadrant_2_max_demand;   // 第二象限最大需量
// double reactive_power_quadrant_2_max_demand_a; // A 相第二象限最大需量
// double reactive_power_quadrant_2_max_demand_b; // B 相第二象限最大需量
// double reactive_power_quadrant_2_max_demand_c; // C 相第二象限最大需量
// double reactive_power_quadrant_3_max_demand;   // 第三象限最大需量
// double reactive_power_quadrant_3_max_demand_a; // A 相第三象限最大需量
// double reactive_power_quadrant_3_max_demand_b; // B 相第三象限最大需量
// double reactive_power_quadrant_3_max_demand_c; // C 相第三象限最大需量
// double reactive_power_quadrant_4_max_demand;   // 第四象限最大需量
// double reactive_power_quadrant_4_max_demand_a; // A 相第四象限最大需量
// double reactive_power_quadrant_4_max_demand_b; // B 相第四象限最大需量
// double reactive_power_quadrant_4_max_demand_c; // C 相第四象限最大需量
// double apparent_power_max_demand_forward;      // 正向视在最大需量
// double apparent_power_max_demand_forward_a;    // A 相正向视在最大需量
// double apparent_power_max_demand_forward_b; //B 相正向视在最大需量
// double apparent_power_max_demand_forward_c; //C 相正向视在最大需量
// double apparent_power_max_demand_reverse;   //反向视在最大需量
// double apparent_power_max_demand_reverse_a; //A 相反向视在最大需量
// double apparent_power_max_demand_reverse_b; //B 相反向视在最大需量
// double apparent_power_max_demand_reverse_c; //C 相反向视在最大需量

// double voltage;                     // 电压
// double voltage_zero_sequence;       // 零序电压
// double current;                     // 电流
// double current_zero_sequence;       // 零序电流
// double voltage_phase_angle;         // 电压相角
// double voltage_current_phase_angle; // 电压电流相角
// double active_power;                // 有功功率
// double reactive_power;              // 无功功率
// double apparent_power;              // 视在功率
// double power_factor;                // 功率因数
// double grid_frequency;              // 电网频率
// int meter_status_word;              // 电能表运行状态字
// double voltage_unbalance_rate;      // 电压不平衡率
// double current_unbalance_rate;      // 电流不平衡率

// double voltage_harmonic_rate_a;    // A 相电压谐波含有率
// double voltage_harmonic_rate_b;    // B 相电压谐波含有率
// double voltage_harmonic_rate_c;    // C 相电压谐波含有率
// double current_harmonic_rate_a;    // A 相电流谐波含有率
// double current_harmonic_rate_b;    // B 相电流谐波含有率
// double current_harmonic_rate_c;    // C 相电流谐波含有率
// double voltage_harmonic_content_a; // A 相电压谐波含量
// double voltage_harmonic_content_b; // B 相电压谐波含量
// double voltage_harmonic_content_c; // C 相电压谐波含量
// double current_harmonic_content_a; // A 相电流谐波含量
// double current_harmonic_content_b; // B 相电流谐波含量
// double current_harmonic_content_c; // C 相电流谐波含量

#endif