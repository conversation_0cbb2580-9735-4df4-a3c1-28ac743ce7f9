/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012.
 */

#ifndef INC_UTIL_STRINGUTIL_H
#define INC_UTIL_STRINGUTIL_H

#ifndef _SIZE_T_DEFINED
#define _SIZE_T_DEFINED
#undef size_t
#ifdef _WIN64
typedef unsigned long long size_t;
#else
typedef unsigned int size_t;
#endif /* _WIN64 */
#endif /* _SIZE_T_DEFINED */

#define MAX_EVENT_TIME_LEN   18
#define MAX_CLIENT_TIME_LEN  12


/* string APIs */
int stringlength(char *str);
char *strInStr(const char *_Str, const char *_SubStr);
int string2int(const char *value);
void *strMemSet(void *_Dst, int _Val, size_t _Size);
void memFree(char **str);
char *combineStrings(int strAmount, char * str1, ...);
char *combineStrings2(char *str1, char *str2);
char *combineStrings3(char *str1, char *str2, char *str3);
char *combineStrings4(char *str1, char *str2, char *str3, char *str4);
void stringMalloc(char **str, int length);
void copyStrValue(char **dst,const char *src, int length);
char *getClientTimeStamp(void);
size_t constStringlength(const char *_Str);
void getEventTimeStamp_s(char *timestamp);
void getClientTimeStamp_s(char *timestamp);


#endif
