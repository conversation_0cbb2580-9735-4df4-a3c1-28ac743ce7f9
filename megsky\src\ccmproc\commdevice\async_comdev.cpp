//=========================================================================================
// JYF-ISG 智能远动机项目，需要考虑串口底层收发机制，设计Linux系统下的串口收发处理类
//
// 尚未测试
//
// suhuaiguang 2014-5
//=========================================================================================
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>   
#include <errno.h>   
#include <termios.h>   
#include <sys/time.h>

#include <assert.h>
#include "commdevice.h"
#include "async_comdev.h"

//=========================================================================================
//串口通信驱动线程类
//=========================================================================================
CPosixSerialComDevThread::CPosixSerialComDevThread(void)
: m_pObject(NULL)
{
}

CPosixSerialComDevThread::~CPosixSerialComDevThread(void)
{
}

void CPosixSerialComDevThread::SetObject(CPosixSerialComDevice * p)
{
	m_pObject = p;
}

void CPosixSerialComDevThread::run(void)
{
	m_pObject->thread_prev();
	while (CSL_thread::IsEnableRun())
	{
		m_pObject->thread_func();
	}
	m_pObject->thread_exit();
}

std::string CPosixSerialComDevThread::ThreadName(void) const
{
	return m_pObject->Name();
}

void CPosixSerialComDevThread::RecordLog(const std::string & /*sMsg*/)
{
}

//=========================================================================================
//POSIX系统的串口通信设备类
//=========================================================================================
CPosixSerialComDevice::CPosixSerialComDevice(CSerialDevInterface * pDI)
: m_pSDI(pDI)
, m_bOpen(false)
, m_bSelfOpen(false)
, m_nComFd(-1)
, m_nPreSendNumber(0)
, m_bRunning(false)
{
	m_thread.SetObject(this);
}

CPosixSerialComDevice::~CPosixSerialComDevice(void)
{
	CloseWatch();
	Close();
}

void CPosixSerialComDevice::SetParam(const CSerialCommParam & r)
{
	m_param = r;
}

bool CPosixSerialComDevice::IsOpen(void)
{
	return m_bOpen;
}

bool CPosixSerialComDevice::Open(void)
{
	if (!m_bOpen) {
		Jint32 nCOMFd(-1);
		CPosixSerialFdManager & com_mgr = CPosixSerialFdManager::Instance();
		com_mgr.GetSerialComDevice(m_param.COM(), nCOMFd);
		if(nCOMFd != -1) {
			m_nComFd = nCOMFd;
			m_bSelfOpen = false; //不是自己打开的串口
			m_bOpen = true;	 // 2015年5月16日 处理COM1、COM2复用
		}
		else{
			char szCOM[128] = {0};
			sprintf(szCOM, "/dev/%s", m_param.COM().c_str());
			m_nComFd = open(szCOM, O_RDWR | O_NOCTTY | O_NDELAY);
			if (-1 != m_nComFd){
				int nRC = fcntl(m_nComFd, F_SETFL, FNDELAY); //设置串口为异步模式
				assert(0 == nRC);
				m_bOpen = true;
				m_bSelfOpen = (-1 != m_nComFd);
				com_mgr.SetSerialComDevice(m_param.COM(), m_nComFd); // 2015年5月16日
			}
			else{
				m_pSDI->OnNotifyDevNotOpen();
			}
		}
	}
	return m_bOpen;
}

bool CPosixSerialComDevice::Close(void)
{
	if (m_bSelfOpen) {
		close(m_nComFd);
		m_bSelfOpen = false;
		CPosixSerialFdManager & com_mgr = CPosixSerialFdManager::Instance();
		com_mgr.SetSerialComDevice(m_param.COM(), -1);
	}

	m_nComFd = -1;
	m_bOpen = false;
	m_pSDI->OnNotifyDisconnect();

	return !m_bOpen;
}

bool CPosixSerialComDevice::Set(void)
{
	bool bRet(false);
	if (m_bOpen)
	{
		//首先获取串口当前参数
		struct termios options;
		int rc = tcgetattr(m_nComFd, &options);
		 
		if (0 == rc)
		{
			// 清空当前参数
			bzero(&options, sizeof(options)); 
			
			//波特率
			int baudrate(B9600);
			switch (m_param.BPS())
			{
			case 300:
  				baudrate = B300;
				break;
			case 600:
  				baudrate = B600;
				break;
			case 1200:
   				baudrate = B1200;
				break;
			case 2400:
   				baudrate = B2400;
   				break;
			case 4800:
   				baudrate = B4800;
				break;
			case 9600:
   				baudrate = B9600;
				break;
			case 115200:
				baudrate = B115200;
				break;
			case 19200:
   				baudrate = B19200;
				break;
			case 38400:
   				baudrate = B38400;
				break;
			default:
   				baudrate = B9600;  
				break;
			}

			//起始位

			//数据位
			options.c_cflag &=~CSIZE;	//清除原数据位    
			switch (m_param.DataBits())
			{
			case 5:  
	   			options.c_cflag |= CS5;
   				break;
			case 6:
	   			options.c_cflag |= CS6;
   				break;
			case 7:
	   			options.c_cflag |= CS7;
   				break;
			case 8:
	   			options.c_cflag |= CS8;
   				break;
			default:
	   			options.c_cflag |= CS8;
   				break;   
			}

			//校验位
			switch (m_param.Parity())
			{   
			case eparity_None: //不校验
   				options.c_cflag &= ~PARENB;
				options.c_iflag &= ~INPCK;
   				break;
			case eparity_Odd:	//奇校验
				options.c_cflag |= (PARODD | PARENB);
   				options.c_iflag |= INPCK;
				break;
			case eparity_Even:	//偶校验
   				options.c_cflag |= PARENB;
				options.c_cflag &= ~PARODD; 
   				options.c_iflag |= INPCK;
				break;
			case eparity_Space:	//始终为0(space)
				options.c_cflag &= ~PARENB;
				options.c_cflag &= ~CSTOPB;
				break;
			case eparity_Mark:	//始终为1(mark)
			default:			//默认不校验
   				options.c_cflag &= ~PARENB;
				options.c_iflag &= ~INPCK;
   				break;   
			}

			//停止位
			switch (m_param.StopBits())
			{   
			case estopbit_onebit:	//1位停止位
				options.c_cflag &= ~CSTOPB;
   				break;
			case estopbit_twobit:	//2位停止位
				options.c_cflag |= CSTOPB;
				break;
			case estopbit_one5bit:	//1.5位停止位
			default:				//默认1位停止位
   				options.c_cflag &= ~CSTOPB; 
				break;
			} 

			options.c_cc[VTIME]	= 0;					//非规范模式读取时的超时时间
			options.c_cc[VMIN]	= 0;					//非规范模式读取时的最小字符数
			options.c_cflag		|= (CLOCAL|CREAD);		//忽略调制解调器线路状态 | 使用接收器
			options.c_oflag		|= OPOST;				//处理后输出
			options.c_oflag		&= ~CRTSCTS;			//使用RTS/CTS流控制
			options.c_iflag		&= ~(IXON|IXOFF|IXANY);	//允许输出时对XON/XOFF流进行控制 | 允许输入时对XON/XOFF流进行控制 | 输入任何字符将重启停止的输出

			cfsetispeed(&options, baudrate);			//设置输入波特率
			cfsetospeed(&options, baudrate);			//设置输出波特率
			tcflush(m_nComFd, TCIFLUSH);				//清除正收到的数据，且不会读取出来

			//设置串口新的参数
			rc = tcsetattr(m_nComFd, TCSANOW, &options);
			bRet = (0 == rc);
		}
	}

	return bRet;
}

bool CPosixSerialComDevice::StartWatch(void)
{
	if (m_bOpen) {
		if (!m_bRunning)
			m_bRunning = m_thread.start();
	}
	return m_bRunning;
}

bool CPosixSerialComDevice::CloseWatch(void)
{
	if (m_bRunning) {
		m_thread.close();
		m_thread.wait_for_end();
		m_bRunning = false;
	}
	return !m_bRunning;
}

std::string CPosixSerialComDevice::Name(void) const
{
	return m_param.COM();
}

void CPosixSerialComDevice::ReadData(void)
{
	struct timeval tv;
	tv.tv_sec	= 0;
	tv.tv_usec	= 1000;

	fd_set fdRead;
	FD_ZERO(&fdRead);
	FD_SET(m_nComFd, &fdRead);
	int rc = select(m_nComFd + 1, &fdRead, NULL, NULL, &tv);
	if (rc > 0) {
		if (FD_ISSET(m_nComFd, &fdRead)) {
			int nRcvLen = read(m_nComFd, m_cRecv, RECV_BUF_LEN);
			if (nRcvLen > 0)
				m_pSDI->OnNotifyRecv((unsigned char *)m_cRecv, static_cast<Juint32>(nRcvLen));
		}
	}
	else if (rc < 0){
		if (EINTR != errno) {
			m_pSDI->OnNotifyError(errno); //通知错误码
			Close(); //出错，要关闭串口
		}
	}
}

void CPosixSerialComDevice::SendData(void)
{
	if (IsOpen()) {
		if (m_nPreSendNumber <=0) {
			unsigned char * pSend = (unsigned char *)m_cSend;
			Juint32 dwSize(SEND_BUF_LEN);
			m_nPreSendNumber = m_pSDI->OnGetContinueSend(pSend, dwSize);
		}

		while (m_nPreSendNumber > 0) {
			int rc = write(m_nComFd, m_cSend, m_nPreSendNumber);
			if (rc > 0)
				m_nPreSendNumber -= rc;
			else if (rc < 0) {
				if (EAGAIN != errno) {
					m_pSDI->OnNotifyError(errno); //通知错误码
					Close(); //出错，要关闭串口
					break;
				}
			}
		}
	}
}

void CPosixSerialComDevice::thread_prev(void)
{
	m_pSDI->OnNotifyConnected();
}

void CPosixSerialComDevice::thread_func(void)
{
	if (m_bOpen) {
		ReadData();
		SendData();
	}
	else {
		ii_sleep(10);
	}

	//通知上层，本线程还在运行
	m_pSDI->OnNotifyAlive();
}

void CPosixSerialComDevice::thread_exit(void)
{
}

void CPosixSerialComDevice::Reset(void)
{
}

bool CPosixSerialComDevice::IsSendComplete(void)
{
	return true;
}

void CPosixSerialComDevice::NotifySendData(void)
{
}
//=========================================================================================
//POSIX系统的串口设备操作符管理类
//=========================================================================================

CPosixSerialFdManager & CPosixSerialFdManager::Instance(void)
{
	static CPosixSerialFdManager mgr;
	return mgr;
}

CPosixSerialFdManager::CPosixSerialFdManager(void)
{
}

CPosixSerialFdManager::~CPosixSerialFdManager(void)
{
}

void CPosixSerialFdManager::SetSerialComDevice(const std::string & sCOM, Jint32 nCOMFd)
{
	CIIAutoMutex auto_lock(&m_lock);
	COMDEVICES::iterator it = m_comdevs.find(sCOM);
	if (m_comdevs.end() == it)
		m_comdevs[sCOM] = nCOMFd;
}

bool CPosixSerialFdManager::GetSerialComDevice(const std::string & sCOM, Jint32 & nCOMFd)
{
	CIIAutoMutex auto_lock(&m_lock);
	nCOMFd = -1;
	COMDEVICES::iterator it = m_comdevs.find(sCOM);
	if (m_comdevs.end() != it)
		nCOMFd = it->second;
	return (-1 != nCOMFd);
}

